# 项目接单管理工具 v2.0

## 使用说明

1. 双击"项目接单管理工具.exe"启动程序
2. 首次运行会自动创建数据库文件
3. 可以通过菜单栏进行数据的导入导出

## 功能特性

- ✅ 项目记录管理（增删改查）
- ✅ 数据筛选和搜索
- ✅ Markdown格式导入导出
- ✅ 统计图表分析（需要matplotlib支持）
- ✅ 多种编程语言支持
- ✅ 北京时间记录
- ✅ 响应式界面设计

## 支持的编程语言

- C语言
- C++
- Java
- Python
- C#
- Android
- 多语言项目

## 数据导入导出

### 导出功能
- 支持导出为Markdown格式
- 按编程语言分组显示
- 包含统计信息

### 导入功能
- 支持从Markdown文件导入
- 自动检测数据格式
- 导入前预览功能

## 系统要求

- Windows 10/11
- 无需安装Python环境
- 建议内存：4GB以上
- 磁盘空间：200MB以上

## 文件说明

- 项目接单管理工具.exe - 主程序
- demo_project_orders.db - 数据库文件（首次运行自动创建）
- README.txt - 本说明文件

## 注意事项

1. 请勿删除数据库文件，否则会丢失所有数据
2. 建议定期导出数据进行备份
3. 如遇到问题，请重启程序

## 技术支持

如有问题或建议，请联系开发团队。

---
版本: 2.0.0
构建时间: 2024年8月19日
开发工具: Python + PyQt5 + PyInstaller
