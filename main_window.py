#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口模块
包含主窗口类和相关功能
"""

import re
from datetime import datetime
from typing import List, Dict, Tuple
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QTableWidget, QTableWidgetItem, QPushButton, QLabel,
                             QComboBox, QLineEdit, QDateEdit, QMessageBox, QHeaderView,
                             QAbstractItemView, QMenuBar, QMenu, QAction, QToolBar,
                             QFileDialog, QInputDialog, QApplication)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QIcon, QFont, QPalette, QColor, QPixmap, QPainter

from config import (PROGRAMMING_LANGUAGES, TABLE_COLUMNS, COLUMN_CONFIG, 
                   LANGUAGE_COLORS, EXPORT_DATE_FORMAT, EXPORT_FILENAME_FORMAT,
                   COLUMN_MAPPINGS, CHINESE_NUMBERS)
from styles import StyleManager
from database import DatabaseManager
from dialogs import AddOrderDialog, EditOrderDialog, ImportPreviewDialog
from statistics_dialog import StatisticsDialog
from import_export import ImportExportMixin


class ProjectOrderManager(QMainWindow, ImportExportMixin):
    """项目接单管理主窗口"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.setWindowTitle("项目接单管理工具")
        self.setGeometry(100, 100, 1200, 700)
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """设置界面"""
        # 应用主窗口样式
        self.setStyleSheet(StyleManager.get_main_style())

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建菜单栏
        self.create_menubar()

        # 工具栏
        self.create_toolbar()

        # 创建筛选面板
        self.create_filter_panel()
        main_layout.addWidget(self.filter_panel)

        # 创建表格
        self.create_table()
        main_layout.addWidget(self.table)

        # 创建状态栏
        self.create_status_bar()

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()

        # 记录数量标签
        self.record_count_label = QLabel("记录数量: 0")
        self.status_bar.addWidget(self.record_count_label)

        # 分隔符
        self.status_bar.addPermanentWidget(QLabel("|"))

        # 总金额标签
        self.total_amount_label = QLabel("总金额: ¥0.00")
        self.status_bar.addPermanentWidget(self.total_amount_label)

        # 分隔符
        self.status_bar.addPermanentWidget(QLabel("|"))

        # 平均金额标签
        self.avg_amount_label = QLabel("平均金额: ¥0.00")
        self.status_bar.addPermanentWidget(self.avg_amount_label)

    def update_status_bar(self):
        """更新状态栏信息"""
        orders = self.db_manager.get_all_orders()
        count = len(orders)
        total_amount = sum(order['amount'] for order in orders)
        avg_amount = total_amount / count if count > 0 else 0

        self.record_count_label.setText(f"记录数量: {count}")
        self.total_amount_label.setText(f"总金额: ¥{total_amount:.2f}")
        self.avg_amount_label.setText(f"平均金额: ¥{avg_amount:.2f}")

    def create_filter_panel(self):
        """创建筛选面板"""
        self.filter_panel = QWidget()
        self.filter_panel.setStyleSheet(StyleManager.get_filter_panel_style())

        # 使用垂直布局来更好地组织筛选控件
        main_filter_layout = QVBoxLayout(self.filter_panel)
        main_filter_layout.setSpacing(10)

        # 第一行：编程语言和报告要求
        first_row_layout = QHBoxLayout()
        first_row_layout.setSpacing(20)

        # 第二行：金额范围和日期范围
        second_row_layout = QHBoxLayout()
        second_row_layout.setSpacing(20)

        # 筛选标题
        filter_title = QLabel("🔍 筛选条件:")
        filter_title.setStyleSheet("font-weight: bold; color: #495057; border: none; padding: 0; margin-bottom: 10px;")
        main_filter_layout.addWidget(filter_title)

        # 第一行：编程语言和报告要求
        # 编程语言筛选
        lang_group = QHBoxLayout()
        lang_label = QLabel("编程语言:")
        lang_label.setStyleSheet("color: #495057; border: none; padding: 0; min-width: 80px;")
        lang_group.addWidget(lang_label)

        self.language_filter = QComboBox()
        self.language_filter.addItem("全部", "")
        for lang in PROGRAMMING_LANGUAGES:
            self.language_filter.addItem(lang, lang)
        self.language_filter.currentIndexChanged.connect(self.apply_filters)
        self.language_filter.setStyleSheet("""
            QComboBox {
                min-width: 150px;
                max-width: 200px;
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
        """)
        lang_group.addWidget(self.language_filter)
        lang_group.addStretch()
        first_row_layout.addLayout(lang_group)

        # 报告要求筛选
        report_group = QHBoxLayout()
        report_label = QLabel("报告要求:")
        report_label.setStyleSheet("color: #495057; border: none; padding: 0; min-width: 80px;")
        report_group.addWidget(report_label)

        self.report_filter = QComboBox()
        self.report_filter.addItem("全部", "")
        self.report_filter.addItem("需要报告", "true")
        self.report_filter.addItem("无需报告", "false")
        self.report_filter.currentIndexChanged.connect(self.apply_filters)
        self.report_filter.setStyleSheet("""
            QComboBox {
                min-width: 120px;
                max-width: 150px;
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
        """)
        report_group.addWidget(self.report_filter)
        report_group.addStretch()
        first_row_layout.addLayout(report_group)

        main_filter_layout.addLayout(first_row_layout)

        # 第二行：金额范围和日期范围
        # 金额范围筛选
        amount_group = QHBoxLayout()
        amount_label = QLabel("金额范围:")
        amount_label.setStyleSheet("color: #495057; border: none; padding: 0; min-width: 80px;")
        amount_group.addWidget(amount_label)

        self.min_amount_edit = QLineEdit()
        self.min_amount_edit.setPlaceholderText("最小金额")
        self.min_amount_edit.setMaximumWidth(120)
        self.min_amount_edit.setStyleSheet(StyleManager.get_input_style())
        self.min_amount_edit.textChanged.connect(self.apply_filters)
        amount_group.addWidget(self.min_amount_edit)

        dash_label = QLabel("-")
        dash_label.setStyleSheet("color: #495057; margin: 0 5px;")
        amount_group.addWidget(dash_label)

        self.max_amount_edit = QLineEdit()
        self.max_amount_edit.setPlaceholderText("最大金额")
        self.max_amount_edit.setMaximumWidth(120)
        self.max_amount_edit.setStyleSheet(StyleManager.get_input_style())
        self.max_amount_edit.textChanged.connect(self.apply_filters)
        amount_group.addWidget(self.max_amount_edit)
        amount_group.addStretch()
        second_row_layout.addLayout(amount_group)

        # 日期范围筛选
        date_group = QHBoxLayout()
        date_label = QLabel("日期范围:")
        date_label.setStyleSheet("color: #495057; border: none; padding: 0; min-width: 80px;")
        date_group.addWidget(date_label)

        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addMonths(-12))
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setMinimumWidth(140)
        self.start_date_edit.setMaximumWidth(160)
        self.start_date_edit.setStyleSheet(StyleManager.get_input_style())
        self.start_date_edit.dateChanged.connect(self.apply_filters)
        date_group.addWidget(self.start_date_edit)

        dash_label2 = QLabel("-")
        dash_label2.setStyleSheet("color: #495057; margin: 0 5px;")
        date_group.addWidget(dash_label2)

        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate().addMonths(12))
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setMinimumWidth(140)
        self.end_date_edit.setMaximumWidth(160)
        self.end_date_edit.setStyleSheet(StyleManager.get_input_style())
        self.end_date_edit.dateChanged.connect(self.apply_filters)
        date_group.addWidget(self.end_date_edit)
        date_group.addStretch()
        second_row_layout.addLayout(date_group)

        main_filter_layout.addLayout(second_row_layout)

        # 第三行：清除筛选按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        clear_filter_btn = QPushButton("🗑️ 清除筛选")
        clear_filter_btn.setStyleSheet(StyleManager.get_button_style("secondary"))
        clear_filter_btn.clicked.connect(self.clear_filters)
        button_layout.addWidget(clear_filter_btn)

        main_filter_layout.addLayout(button_layout)

        # 初始化筛选状态
        self.filtered_orders = []

    def create_menubar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu('文件')

        # 添加记录
        add_action = QAction('添加记录', self)
        add_action.setShortcut('Ctrl+N')
        add_action.triggered.connect(self.add_order)
        file_menu.addAction(add_action)

        file_menu.addSeparator()

        # 导出Markdown
        export_action = QAction('导出Markdown文档', self)
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.export_markdown)
        file_menu.addAction(export_action)

        # 导入Markdown
        import_action = QAction('导入Markdown文档', self)
        import_action.setShortcut('Ctrl+I')
        import_action.triggered.connect(self.import_markdown)
        file_menu.addAction(import_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 编辑菜单
        edit_menu = menubar.addMenu('编辑')

        # 删除记录
        delete_action = QAction('删除记录', self)
        delete_action.setShortcut('Delete')
        delete_action.triggered.connect(self.delete_selected_orders)
        edit_menu.addAction(delete_action)

        # 刷新
        refresh_action = QAction('刷新', self)
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.load_data)
        edit_menu.addAction(refresh_action)

    def create_toolbar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('主工具栏')
        toolbar.setMovable(False)
        toolbar.setFloatable(False)

        # 添加记录按钮
        add_btn = QPushButton('📝 添加记录')
        add_btn.setToolTip('添加新的接单记录 (Ctrl+N)')
        add_btn.setStyleSheet(StyleManager.get_button_style("success"))
        add_btn.clicked.connect(self.add_order)
        toolbar.addWidget(add_btn)

        # 添加间距
        toolbar.addSeparator()

        # 删除记录按钮
        delete_btn = QPushButton('🗑️ 删除记录')
        delete_btn.setToolTip('删除选中的记录 (Delete)')
        delete_btn.setStyleSheet(StyleManager.get_button_style("danger"))
        delete_btn.clicked.connect(self.delete_selected_orders)
        toolbar.addWidget(delete_btn)

        # 添加间距
        toolbar.addSeparator()

        # 刷新按钮
        refresh_btn = QPushButton('🔄 刷新')
        refresh_btn.setToolTip('刷新数据 (F5)')
        refresh_btn.setStyleSheet(StyleManager.get_button_style("info"))
        refresh_btn.clicked.connect(self.load_data)
        toolbar.addWidget(refresh_btn)

        # 添加弹性空间，将后续按钮推到右侧
        spacer = QWidget()
        spacer.setSizePolicy(QWidget().sizePolicy().Expanding, QWidget().sizePolicy().Preferred)
        toolbar.addWidget(spacer)

        # 导出按钮
        export_btn = QPushButton('📄 导出')
        export_btn.setToolTip('导出Markdown文档')
        export_btn.setStyleSheet(StyleManager.get_button_style("warning"))
        export_btn.clicked.connect(self.export_markdown)
        toolbar.addWidget(export_btn)

        # 导入按钮
        import_btn = QPushButton('📥 导入')
        import_btn.setToolTip('从Markdown文档导入数据')
        import_btn.setStyleSheet("""
            QPushButton {
                background-color: #20c997;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 600;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #1ba085;
            }
            QPushButton:pressed {
                background-color: #198c73;
            }
        """)
        import_btn.clicked.connect(self.import_markdown)
        toolbar.addWidget(import_btn)

        # 统计信息按钮
        stats_btn = QPushButton('📊 统计')
        stats_btn.setToolTip('查看统计信息')
        stats_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 600;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
            QPushButton:pressed {
                background-color: #4e2a8e;
            }
        """)
        stats_btn.clicked.connect(self.show_statistics)
        toolbar.addWidget(stats_btn)

    def create_table(self):
        """创建表格"""
        self.table = QTableWidget()

        # 设置列
        self.columns = TABLE_COLUMNS
        self.table.setColumnCount(len(self.columns))
        self.table.setHorizontalHeaderLabels(self.columns)

        # 隐藏ID列
        self.table.setColumnHidden(0, True)

        # 设置表格属性
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)
        self.table.setShowGrid(True)
        self.table.setWordWrap(True)

        # 应用表格样式
        self.table.setStyleSheet(StyleManager.get_table_style())

        # 设置响应式列宽
        self.setup_responsive_columns()

        # 双击编辑
        self.table.itemDoubleClicked.connect(self.edit_item)

        # 设置行高
        self.table.verticalHeader().setDefaultSectionSize(40)
        self.table.verticalHeader().hide()

    def setup_responsive_columns(self):
        """设置响应式列宽"""
        header = self.table.horizontalHeader()

        # 应用列宽配置
        for col_index, (min_width, max_width, resize_mode) in COLUMN_CONFIG.items():
            if resize_mode == 'ResizeToContents':
                header.setSectionResizeMode(col_index, QHeaderView.ResizeToContents)
            elif resize_mode == 'Stretch':
                header.setSectionResizeMode(col_index, QHeaderView.Stretch)
            elif resize_mode == 'Interactive':
                header.setSectionResizeMode(col_index, QHeaderView.Interactive)

            header.resizeSection(col_index, min_width)
            if resize_mode == 'Interactive':
                # 为可交互列设置最小和最大宽度
                header.setMinimumSectionSize(min_width)

        # 设置表头可拖拽调整
        header.setSectionsMovable(False)  # 禁止拖拽移动列
        header.setSectionsClickable(True)  # 允许点击排序

        # 最后一列不自动拉伸，使用固定宽度
        header.setStretchLastSection(False)

    def load_data(self):
        """加载数据到表格"""
        orders = self.db_manager.get_all_orders()
        self.table.setRowCount(len(orders))

        for row, order in enumerate(orders):
            # ID (隐藏列)
            self.table.setItem(row, 0, QTableWidgetItem(str(order['id'])))

            # 入库编号
            order_num_item = QTableWidgetItem(order['order_number'])
            order_num_item.setFont(QFont("Consolas", 9, QFont.Bold))
            self.table.setItem(row, 1, order_num_item)

            # 交付日期
            date_item = QTableWidgetItem(order['delivery_date'])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(row, 2, date_item)

            # 需求描述 - 添加省略号处理
            from utils import truncate_text
            desc_text = order['requirement_desc']
            desc_display = truncate_text(desc_text, 50)
            desc_item = QTableWidgetItem(desc_display)
            desc_item.setToolTip(desc_text)  # 完整文本作为提示
            self.table.setItem(row, 3, desc_item)

            # 金额 - 使用绿色显示
            amount_item = QTableWidgetItem(f"¥{order['amount']:.2f}")
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            amount_item.setForeground(QColor("#28a745"))  # 绿色
            amount_item.setFont(QFont("Arial", 9, QFont.Bold))
            self.table.setItem(row, 4, amount_item)

            # 是否需要报告 - 使用不同颜色标识
            report_text = "是" if order['need_report'] else "否"
            report_item = QTableWidgetItem(report_text)
            report_item.setTextAlignment(Qt.AlignCenter)
            if order['need_report']:
                report_item.setForeground(QColor("#dc3545"))  # 红色表示需要报告
                report_item.setFont(QFont("Arial", 9, QFont.Bold))
            else:
                report_item.setForeground(QColor("#6c757d"))  # 灰色表示不需要报告
            self.table.setItem(row, 5, report_item)

            # 联系方式
            contact_item = QTableWidgetItem(order['contact_info'])
            contact_item.setForeground(QColor("#007bff"))  # 蓝色
            self.table.setItem(row, 6, contact_item)

            # 备注 - 添加省略号处理
            remarks_text = order['remarks'] or ''
            remarks_display = truncate_text(remarks_text, 30)
            remarks_item = QTableWidgetItem(remarks_display)
            remarks_item.setToolTip(remarks_text)
            remarks_item.setForeground(QColor("#6c757d"))  # 灰色
            self.table.setItem(row, 7, remarks_item)

            # 编程语言 - 使用不同颜色标识
            lang_item = QTableWidgetItem(order['programming_language'])
            lang_item.setTextAlignment(Qt.AlignCenter)
            lang_item.setFont(QFont("Arial", 8, QFont.Bold))
            # 根据编程语言设置不同颜色
            color = LANGUAGE_COLORS.get(order['programming_language'], '#6c757d')
            lang_item.setForeground(QColor(color))
            self.table.setItem(row, 8, lang_item)

            # 创建时间（北京时间）
            created_at = order.get('created_at', '')
            if created_at:
                # 格式化创建时间显示
                try:
                    if ' ' in created_at:
                        # 如果包含时间部分，格式化为更友好的显示
                        dt = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                        formatted_time = dt.strftime('%m-%d %H:%M')
                    else:
                        # 只有日期部分
                        formatted_time = created_at
                except:
                    formatted_time = created_at
            else:
                formatted_time = ''

            created_item = QTableWidgetItem(formatted_time)
            created_item.setTextAlignment(Qt.AlignCenter)
            created_item.setForeground(QColor("#6c757d"))  # 灰色
            created_item.setFont(QFont("Arial", 8))
            created_item.setToolTip(f"创建时间（北京时间）: {created_at}")
            self.table.setItem(row, 9, created_item)

        # 更新状态栏
        self.update_status_bar()

        # 保存原始数据用于筛选（按创建时间倒序）
        self.all_orders = orders.copy()
        self.filtered_orders = orders.copy()

        # 应用当前的筛选条件（如果有的话）
        if hasattr(self, 'language_filter') and self.language_filter.currentIndex() > 0:
            self.apply_filters()

    def apply_filters(self):
        """应用筛选条件 - 使用数据库查询"""
        try:
            # 获取筛选条件
            selected_language = self.language_filter.currentData()
            language_filter = selected_language if selected_language else None

            # 金额范围筛选
            min_amount = None
            max_amount = None

            min_amount_text = self.min_amount_edit.text().strip()
            max_amount_text = self.max_amount_edit.text().strip()

            try:
                if min_amount_text:
                    min_amount = float(min_amount_text)
            except ValueError:
                pass

            try:
                if max_amount_text:
                    max_amount = float(max_amount_text)
            except ValueError:
                pass

            # 日期范围筛选
            start_date = self.start_date_edit.date().toString('yyyy-MM-dd')
            end_date = self.end_date_edit.date().toString('yyyy-MM-dd')

            # 报告要求筛选
            report_filter = self.report_filter.currentData()

            # 使用数据库查询获取筛选结果
            filtered_orders = self.db_manager.get_filtered_orders(
                language_filter=language_filter,
                min_amount=min_amount,
                max_amount=max_amount,
                start_date=start_date,
                end_date=end_date,
                report_filter=report_filter
            )

            # 更新显示
            self.filtered_orders = filtered_orders
            self.display_filtered_data(filtered_orders)

        except Exception as e:
            print(f"筛选过程中发生错误: {e}")
            # 如果筛选失败，显示所有数据
            self.load_data()

    def clear_filters(self):
        """清除所有筛选条件"""
        # 重置筛选控件
        self.language_filter.setCurrentIndex(0)
        self.min_amount_edit.clear()
        self.max_amount_edit.clear()
        self.start_date_edit.setDate(QDate.currentDate().addMonths(-12))
        self.end_date_edit.setDate(QDate.currentDate().addMonths(12))
        self.report_filter.setCurrentIndex(0)

        # 显示所有数据
        if hasattr(self, 'all_orders'):
            self.filtered_orders = self.all_orders.copy()
            self.display_filtered_data(self.all_orders)

    def display_filtered_data(self, orders):
        """显示筛选后的数据"""
        from utils import truncate_text

        self.table.setRowCount(len(orders))

        for row, order in enumerate(orders):
            # ID (隐藏列)
            self.table.setItem(row, 0, QTableWidgetItem(str(order['id'])))

            # 入库编号
            order_num_item = QTableWidgetItem(order['order_number'])
            order_num_item.setFont(QFont("Consolas", 9, QFont.Bold))
            self.table.setItem(row, 1, order_num_item)

            # 交付日期
            date_item = QTableWidgetItem(order['delivery_date'])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(row, 2, date_item)

            # 需求描述 - 添加省略号处理
            desc_text = order['requirement_desc']
            desc_display = truncate_text(desc_text, 50)
            desc_item = QTableWidgetItem(desc_display)
            desc_item.setToolTip(desc_text)  # 完整文本作为提示
            self.table.setItem(row, 3, desc_item)

            # 金额 - 使用绿色显示
            amount_item = QTableWidgetItem(f"¥{order['amount']:.2f}")
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            amount_item.setForeground(QColor("#28a745"))  # 绿色
            amount_item.setFont(QFont("Arial", 9, QFont.Bold))
            self.table.setItem(row, 4, amount_item)

            # 是否需要报告 - 使用不同颜色标识
            report_text = "是" if order['need_report'] else "否"
            report_item = QTableWidgetItem(report_text)
            report_item.setTextAlignment(Qt.AlignCenter)
            if order['need_report']:
                report_item.setForeground(QColor("#dc3545"))  # 红色表示需要报告
                report_item.setFont(QFont("Arial", 9, QFont.Bold))
            else:
                report_item.setForeground(QColor("#6c757d"))  # 灰色表示不需要报告
            self.table.setItem(row, 5, report_item)

            # 联系方式
            contact_item = QTableWidgetItem(order['contact_info'])
            contact_item.setForeground(QColor("#007bff"))  # 蓝色
            self.table.setItem(row, 6, contact_item)

            # 备注 - 添加省略号处理
            remarks_text = order['remarks'] or ''
            remarks_display = truncate_text(remarks_text, 30)
            remarks_item = QTableWidgetItem(remarks_display)
            remarks_item.setToolTip(remarks_text)
            remarks_item.setForeground(QColor("#6c757d"))  # 灰色
            self.table.setItem(row, 7, remarks_item)

            # 编程语言 - 使用不同颜色标识
            lang_item = QTableWidgetItem(order['programming_language'])
            lang_item.setTextAlignment(Qt.AlignCenter)
            lang_item.setFont(QFont("Arial", 8, QFont.Bold))
            # 根据编程语言设置不同颜色
            color = LANGUAGE_COLORS.get(order['programming_language'], '#6c757d')
            lang_item.setForeground(QColor(color))
            self.table.setItem(row, 8, lang_item)

            # 创建时间（北京时间）
            created_at = order.get('created_at', '')
            if created_at:
                # 格式化创建时间显示
                try:
                    if ' ' in created_at:
                        # 如果包含时间部分，格式化为更友好的显示
                        dt = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                        formatted_time = dt.strftime('%m-%d %H:%M')
                    else:
                        # 只有日期部分
                        formatted_time = created_at
                except:
                    formatted_time = created_at
            else:
                formatted_time = ''

            created_item = QTableWidgetItem(formatted_time)
            created_item.setTextAlignment(Qt.AlignCenter)
            created_item.setForeground(QColor("#6c757d"))  # 灰色
            created_item.setFont(QFont("Arial", 8))
            created_item.setToolTip(f"创建时间（北京时间）: {created_at}")
            self.table.setItem(row, 9, created_item)

        # 更新状态栏（使用筛选后的数据）
        self.update_filtered_status_bar(orders)

    def update_filtered_status_bar(self, filtered_orders):
        """更新筛选后的状态栏信息"""
        count = len(filtered_orders)
        total_amount = sum(order['amount'] for order in filtered_orders)
        avg_amount = total_amount / count if count > 0 else 0

        # 显示筛选信息
        total_count = len(self.all_orders) if hasattr(self, 'all_orders') else 0
        if count < total_count:
            self.record_count_label.setText(f"记录数量: {count}/{total_count} (已筛选)")
        else:
            self.record_count_label.setText(f"记录数量: {count}")

        self.total_amount_label.setText(f"总金额: ¥{total_amount:.2f}")
        self.avg_amount_label.setText(f"平均金额: ¥{avg_amount:.2f}")

    def add_order(self):
        """添加新记录"""
        dialog = AddOrderDialog(self.db_manager, self)
        if dialog.exec_() == QDialog.Accepted:
            # 显示加载提示
            self.status_bar.showMessage("正在添加记录...", 2000)
            self.load_data()
            # 重新应用筛选
            self.apply_filters()

            # 显示成功消息
            msg = QMessageBox(self)
            msg.setIcon(QMessageBox.Information)
            msg.setWindowTitle("操作成功")
            msg.setText("✅ 记录添加成功！")
            msg.setStyleSheet("""
                QMessageBox {
                    background-color: #ffffff;
                }
                QMessageBox QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background-color: #218838;
                }
            """)
            msg.exec_()

    def edit_item(self, item):
        """编辑表格项 - 使用编辑对话框"""
        if item.column() == 0:  # ID列不可编辑
            return

        row = item.row()

        # 获取当前记录的ID
        id_item = self.table.item(row, 0)
        if not id_item:
            return

        order_id = int(id_item.text())

        # 获取当前记录的完整数据
        orders = self.db_manager.get_all_orders()
        current_order = None
        for order in orders:
            if order['id'] == order_id:
                current_order = order
                break

        if not current_order:
            QMessageBox.warning(self, "错误", "无法找到该记录！")
            return

        # 打开编辑对话框
        dialog = EditOrderDialog(current_order, self.db_manager, self)
        if dialog.exec_() == QDialog.Accepted:
            # 刷新数据显示
            self.load_data()
            self.apply_filters()  # 重新应用筛选条件

    def show_statistics(self):
        """显示统计信息"""
        dialog = StatisticsDialog(self.db_manager, self)
        dialog.exec_()

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 如果有未保存的更改，可以在这里添加确认对话框
        # 目前直接接受关闭事件
        event.accept()
