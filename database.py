#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理模块
包含所有数据库操作相关的功能
"""

import sqlite3
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Optional
from config import DEFAULT_DB_PATH, LANGUAGE_PREFIXES, LANGUAGE_BASE_NUMBERS


class DatabaseManager:
    """数据库管理类"""

    def __init__(self, db_path: str = DEFAULT_DB_PATH):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """初始化数据库表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建接单记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_number TEXT UNIQUE NOT NULL,
                delivery_date TEXT NOT NULL,
                requirement_desc TEXT NOT NULL,
                amount REAL NOT NULL,
                need_report BOOLEAN NOT NULL,
                contact_info TEXT NOT NULL,
                remarks TEXT,
                programming_language TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()
        conn.close()

    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        return conn

    def get_next_order_number(self, programming_language: str) -> str:
        """根据编程语言生成下一个入库编号"""
        prefix = LANGUAGE_PREFIXES.get(programming_language, 'X')
        base_number = LANGUAGE_BASE_NUMBERS.get(programming_language, 1001)

        conn = self.get_connection()
        cursor = conn.cursor()

        # 查找该语言的最大编号
        cursor.execute('''
            SELECT order_number FROM orders
            WHERE programming_language = ? AND order_number LIKE ?
            ORDER BY order_number DESC LIMIT 1
        ''', (programming_language, f'{prefix}%'))

        result = cursor.fetchone()
        conn.close()

        if result:
            # 提取数字部分并加1
            last_number = result['order_number']
            number_part = ''.join(filter(str.isdigit, last_number))
            if number_part:
                next_number = int(number_part) + 1
            else:
                next_number = base_number
        else:
            next_number = base_number

        return f"{prefix}{next_number}"

    def get_beijing_time(self) -> str:
        """获取北京时间字符串"""
        # 创建北京时区（UTC+8）
        beijing_tz = timezone(timedelta(hours=8))
        
        # 获取当前北京时间
        beijing_time = datetime.now(beijing_tz)
        
        # 格式化为字符串
        return beijing_time.strftime('%Y-%m-%d %H:%M:%S')

    def add_order(self, order_data: Dict) -> bool:
        """添加新的接单记录"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 获取北京时间
            beijing_time = self.get_beijing_time()

            cursor.execute('''
                INSERT INTO orders (order_number, delivery_date, requirement_desc,
                                  amount, need_report, contact_info, remarks,
                                  programming_language, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_data['order_number'],
                order_data['delivery_date'],
                order_data['requirement_desc'],
                order_data['amount'],
                order_data['need_report'],
                order_data['contact_info'],
                order_data['remarks'],
                order_data['programming_language'],
                beijing_time,
                beijing_time
            ))

            conn.commit()
            conn.close()
            return True
        except sqlite3.IntegrityError:
            return False
        except Exception as e:
            print(f"添加记录时出错: {e}")
            return False

    def get_all_orders(self) -> List[Dict]:
        """获取所有接单记录"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM orders ORDER BY created_at DESC
        ''')

        orders = []
        for row in cursor.fetchall():
            orders.append(dict(row))

        conn.close()
        return orders

    def get_filtered_orders(self, language_filter=None, min_amount=None, max_amount=None,
                           start_date=None, end_date=None, report_filter=None) -> List[Dict]:
        """根据筛选条件获取接单记录"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # 构建SQL查询
        where_conditions = []
        params = []

        # 编程语言筛选
        if language_filter:
            where_conditions.append("programming_language = ?")
            params.append(language_filter)

        # 金额范围筛选
        if min_amount is not None:
            where_conditions.append("amount >= ?")
            params.append(min_amount)

        if max_amount is not None:
            where_conditions.append("amount <= ?")
            params.append(max_amount)

        # 日期范围筛选
        if start_date:
            where_conditions.append("delivery_date >= ?")
            params.append(start_date)

        if end_date:
            where_conditions.append("delivery_date <= ?")
            params.append(end_date)

        # 报告要求筛选
        if report_filter == "true":
            where_conditions.append("need_report = 1")
        elif report_filter == "false":
            where_conditions.append("need_report = 0")

        # 构建完整的SQL查询
        sql = "SELECT * FROM orders"
        if where_conditions:
            sql += " WHERE " + " AND ".join(where_conditions)
        sql += " ORDER BY created_at DESC"

        cursor.execute(sql, params)

        orders = []
        for row in cursor.fetchall():
            orders.append(dict(row))

        conn.close()
        return orders

    def update_order(self, order_id: int, order_data: Dict) -> bool:
        """更新接单记录"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 获取北京时间
            beijing_time = self.get_beijing_time()

            cursor.execute('''
                UPDATE orders SET
                    order_number = ?, delivery_date = ?, requirement_desc = ?,
                    amount = ?, need_report = ?, contact_info = ?, remarks = ?,
                    programming_language = ?, updated_at = ?
                WHERE id = ?
            ''', (
                order_data['order_number'],
                order_data['delivery_date'],
                order_data['requirement_desc'],
                order_data['amount'],
                order_data['need_report'],
                order_data['contact_info'],
                order_data['remarks'],
                order_data['programming_language'],
                beijing_time,
                order_id
            ))

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"更新记录时出错: {e}")
            return False

    def delete_orders(self, order_ids: List[int]) -> bool:
        """删除接单记录"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            placeholders = ','.join(['?'] * len(order_ids))
            cursor.execute(f'DELETE FROM orders WHERE id IN ({placeholders})', order_ids)

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"删除记录时出错: {e}")
            return False

    def check_order_number_exists(self, order_number: str, exclude_id: int = None) -> bool:
        """检查入库编号是否已存在"""
        conn = self.get_connection()
        cursor = conn.cursor()

        if exclude_id:
            cursor.execute('SELECT id FROM orders WHERE order_number = ? AND id != ?',
                         (order_number, exclude_id))
        else:
            cursor.execute('SELECT id FROM orders WHERE order_number = ?', (order_number,))

        result = cursor.fetchone()
        conn.close()
        return result is not None
