#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置模块
包含应用程序的配置信息和常量定义
"""

import sys
import platform

# 尝试导入matplotlib，如果失败则提供备用方案
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib
    matplotlib.use('Qt5Agg')

    # 设置中文字体支持
    matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS']
    matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

    MATPLOTLIB_AVAILABLE = True

    def setup_matplotlib_chinese():
        """设置matplotlib中文字体"""
        system = platform.system()

        if system == "Windows":
            # Windows系统字体，优先使用Microsoft YaHei（对¥符号支持更好）
            fonts = ['Microsoft YaHei', 'SimHei', 'KaiTi', 'FangSong']
        elif system == "Darwin":  # macOS
            # macOS系统字体
            fonts = ['Arial Unicode MS', 'Heiti TC', 'PingFang SC']
        else:  # Linux
            # Linux系统字体
            fonts = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'Noto Sans CJK SC']

        # 尝试设置字体
        for font in fonts:
            try:
                matplotlib.rcParams['font.sans-serif'] = [font] + matplotlib.rcParams['font.sans-serif']
                print(f"matplotlib字体设置为: {font}")
                break
            except:
                continue

        # 设置负号正常显示
        matplotlib.rcParams['axes.unicode_minus'] = False

        # 设置字体大小
        matplotlib.rcParams['font.size'] = 10
        matplotlib.rcParams['axes.titlesize'] = 14
        matplotlib.rcParams['axes.labelsize'] = 12

    # 调用字体设置函数
    setup_matplotlib_chinese()

except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("警告: matplotlib未安装，统计图表功能将不可用")


# 应用程序常量
APP_NAME = "项目接单管理工具"
APP_VERSION = "2.0"
ORGANIZATION_NAME = "项目管理工具"

# 数据库配置
DEFAULT_DB_PATH = "demo_project_orders.db"

# 支持的编程语言
PROGRAMMING_LANGUAGES = [
    'C语言', 'C++', 'Java', 'Python', '多语言项目', 'C#', 'Android'
]

# 编程语言前缀映射
LANGUAGE_PREFIXES = {
    'C语言': 'C',
    'C++': 'C',
    'Java': 'J',
    'Python': 'P',
    '多语言项目': 'X',
    'C#': 'CC',
    'Android': 'A'
}

# 编程语言基础编号
LANGUAGE_BASE_NUMBERS = {
    'C语言': 1001,
    'C++': 2001,
    'Java': 1001,
    'Python': 1001,
    '多语言项目': 1001,
    'C#': 1001,
    'Android': 1001
}

# 编程语言颜色映射
LANGUAGE_COLORS = {
    'C语言': '#ff6b6b',
    'C++': '#4ecdc4',
    'Java': '#45b7d1',
    'Python': '#96ceb4',
    'C#': '#ffeaa7',
    'Android': '#a29bfe',
    '多语言项目': '#fd79a8'
}

# 表格列配置
TABLE_COLUMNS = ['ID', '入库编号', '交付日期', '需求描述', '金额',
                '是否需要报告', '联系方式', '备注', '编程语言', '创建时间']

# 列宽配置：(列索引, 最小宽度, 最大宽度, 拉伸模式)
COLUMN_CONFIG = {
    1: (100, 150, 'ResizeToContents'),    # 入库编号
    2: (100, 120, 'ResizeToContents'),    # 交付日期
    3: (200, 400, 'Stretch'),             # 需求描述
    4: (80, 120, 'ResizeToContents'),     # 金额
    5: (80, 120, 'ResizeToContents'),     # 是否需要报告
    6: (120, 200, 'Interactive'),         # 联系方式
    7: (100, 300, 'Interactive'),         # 备注
    8: (80, 120, 'ResizeToContents'),     # 编程语言
    9: (140, 180, 'ResizeToContents'),    # 创建时间
}

# 字体配置
def get_default_font():
    """获取默认字体"""
    if sys.platform == 'win32':
        return "Microsoft YaHei"
    elif sys.platform == 'darwin':
        return "PingFang SC"
    else:
        return "DejaVu Sans"

# 导出相关常量
EXPORT_DATE_FORMAT = '%Y年%m月%d日 %H:%M:%S'
EXPORT_FILENAME_FORMAT = '项目接单记录_{}.md'

# 列名映射（用于导入功能）
COLUMN_MAPPINGS = {
    '入库编号': 'order_number',
    '编号': 'order_number',
    '项目编号': 'order_number',
    '交付日期': 'delivery_date',
    '日期': 'delivery_date',
    '完成日期': 'delivery_date',
    '需求描述': 'requirement_desc',
    '描述': 'requirement_desc',
    '项目描述': 'requirement_desc',
    '需求': 'requirement_desc',
    '金额': 'amount',
    '价格': 'amount',
    '费用': 'amount',
    '是否需要报告': 'need_report',
    '需要报告': 'need_report',
    '报告': 'need_report',
    '联系方式': 'contact_info',
    '联系人': 'contact_info',
    '客户': 'contact_info',
    '备注': 'remarks',
    '说明': 'remarks',
    '注释': 'remarks'
}

# 中文数字映射
CHINESE_NUMBERS = {
    '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
    '六': '6', '七': '7', '八': '8', '九': '9', '零': '0',
    '十': '10', '百': '100', '千': '1000', '万': '10000'
}
