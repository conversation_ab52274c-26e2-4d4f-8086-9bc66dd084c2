#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
样式管理模块
包含应用程序的所有UI样式定义
"""


class StyleManager:
    """样式管理类"""

    @staticmethod
    def get_main_style():
        """获取主窗口样式"""
        return """
        QMainWindow {
            background-color: #f8f9fa;
            color: #212529;
        }

        QMenuBar {
            background-color: #ffffff;
            border-bottom: 1px solid #dee2e6;
            padding: 4px;
        }

        QMenuBar::item {
            background-color: transparent;
            padding: 8px 12px;
            border-radius: 4px;
        }

        QMenuBar::item:selected {
            background-color: #e9ecef;
        }

        QToolBar {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 8px;
            spacing: 8px;
        }

        QPushButton {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 500;
            min-width: 80px;
        }

        QPushButton:hover {
            background-color: #0056b3;
        }

        QPushButton:pressed {
            background-color: #004085;
        }

        QPushButton:disabled {
            background-color: #6c757d;
        }

        QStatusBar {
            background-color: #ffffff;
            border-top: 1px solid #dee2e6;
            padding: 4px;
        }
        """

    @staticmethod
    def get_table_style():
        """获取表格样式"""
        return """
        QTableWidget {
            background-color: #ffffff;
            alternate-background-color: #f8f9fa;
            selection-background-color: #007bff;
            selection-color: white;
            gridline-color: #dee2e6;
            border: 1px solid #dee2e6;
            border-radius: 6px;
        }

        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #f1f3f4;
        }

        QTableWidget::item:hover {
            background-color: #e3f2fd;
        }

        QTableWidget::item:selected {
            background-color: #007bff;
            color: white;
        }

        QHeaderView::section {
            background-color: #495057;
            color: white;
            padding: 10px;
            border: none;
            font-weight: 600;
            text-align: left;
        }

        QHeaderView::section:hover {
            background-color: #343a40;
        }

        QScrollBar:vertical {
            background-color: #f8f9fa;
            width: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:vertical {
            background-color: #6c757d;
            border-radius: 6px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background-color: #495057;
        }

        QScrollBar:horizontal {
            background-color: #f8f9fa;
            height: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:horizontal {
            background-color: #6c757d;
            border-radius: 6px;
            min-width: 20px;
        }

        QScrollBar::handle:horizontal:hover {
            background-color: #495057;
        }
        """

    @staticmethod
    def get_dialog_style():
        """获取对话框样式"""
        return """
        QDialog {
            background-color: #ffffff;
        }

        QLabel {
            color: #495057;
            font-weight: 500;
        }

        QLineEdit, QTextEdit, QComboBox, QDateEdit {
            border: 2px solid #ced4da;
            border-radius: 6px;
            padding: 8px;
            background-color: #ffffff;
            selection-background-color: #007bff;
        }

        QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus {
            border-color: #007bff;
            outline: none;
        }

        QCheckBox {
            spacing: 8px;
        }

        QCheckBox::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #ced4da;
            border-radius: 4px;
            background-color: #ffffff;
        }

        QCheckBox::indicator:checked {
            background-color: #007bff;
            border-color: #007bff;
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
        }

        QDialogButtonBox QPushButton {
            min-width: 100px;
            margin: 4px;
        }
        """

    @staticmethod
    def get_button_style(color_type="primary"):
        """获取按钮样式"""
        styles = {
            "primary": {
                "background": "#007bff",
                "hover": "#0056b3",
                "pressed": "#004085"
            },
            "success": {
                "background": "#28a745",
                "hover": "#218838",
                "pressed": "#1e7e34"
            },
            "danger": {
                "background": "#dc3545",
                "hover": "#c82333",
                "pressed": "#bd2130"
            },
            "warning": {
                "background": "#ffc107",
                "hover": "#e0a800",
                "pressed": "#d39e00"
            },
            "info": {
                "background": "#17a2b8",
                "hover": "#138496",
                "pressed": "#117a8b"
            },
            "secondary": {
                "background": "#6c757d",
                "hover": "#5a6268",
                "pressed": "#495057"
            }
        }
        
        style = styles.get(color_type, styles["primary"])
        
        return f"""
        QPushButton {{
            background-color: {style["background"]};
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 600;
            min-width: 100px;
        }}
        QPushButton:hover {{
            background-color: {style["hover"]};
        }}
        QPushButton:pressed {{
            background-color: {style["pressed"]};
        }}
        """

    @staticmethod
    def get_input_style():
        """获取输入框样式"""
        return """
        QLineEdit, QTextEdit, QComboBox, QDateEdit {
            border: 2px solid #ced4da;
            border-radius: 6px;
            padding: 8px;
            background-color: #ffffff;
            selection-background-color: #007bff;
        }
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus {
            border-color: #007bff;
            outline: none;
        }
        """

    @staticmethod
    def get_checkbox_style():
        """获取复选框样式"""
        return """
        QCheckBox {
            spacing: 8px;
        }
        QCheckBox::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #ced4da;
            border-radius: 3px;
            background-color: #ffffff;
        }
        QCheckBox::indicator:checked {
            background-color: #007bff;
            border-color: #007bff;
        }
        """

    @staticmethod
    def get_filter_panel_style():
        """获取筛选面板样式"""
        return """
        QWidget {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 5px 0;
        }
        """

    @staticmethod
    def get_group_box_style():
        """获取分组框样式"""
        return """
        QWidget {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin: 5px;
            padding: 10px;
        }
        """
