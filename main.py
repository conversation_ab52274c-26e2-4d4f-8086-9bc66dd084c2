#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目接单管理工具
使用PyQt5开发的项目管理系统
"""

import sys
import sqlite3
import os
import re
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QTableWidget, QTableWidgetItem,
                             QPushButton, QDialog, QFormLayout, QLineEdit,
                             QComboBox, QDateEdit, QCheckBox, QTextEdit,
                             QMessageBox, QHeaderView, QAbstractItemView,
                             QMenuBar, QMenu, QAction, QToolBar, QLabel,
                             QFileDialog, QDialogButtonBox, QInputDialog)
from PyQt5.QtCore import Qt, QDate, pyqtSignal, QTimer
from PyQt5.QtGui import QIcon, QFont, QPalette, QColor, QPixmap, QPainter

# 尝试导入matplotlib，如果失败则提供备用方案
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib
    matplotlib.use('Qt5Agg')

    # 设置中文字体支持
    matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS']
    matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

    MATPLOTLIB_AVAILABLE = True

    def setup_matplotlib_chinese():
        """设置matplotlib中文字体"""
        import platform
        system = platform.system()

        if system == "Windows":
            # Windows系统字体，优先使用Microsoft YaHei（对¥符号支持更好）
            fonts = ['Microsoft YaHei', 'SimHei', 'KaiTi', 'FangSong']
        elif system == "Darwin":  # macOS
            # macOS系统字体
            fonts = ['Arial Unicode MS', 'Heiti TC', 'PingFang SC']
        else:  # Linux
            # Linux系统字体
            fonts = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'Noto Sans CJK SC']

        # 尝试设置字体
        for font in fonts:
            try:
                matplotlib.rcParams['font.sans-serif'] = [font] + matplotlib.rcParams['font.sans-serif']
                print(f"matplotlib字体设置为: {font}")
                break
            except:
                continue

        # 设置负号正常显示
        matplotlib.rcParams['axes.unicode_minus'] = False

        # 设置字体大小
        matplotlib.rcParams['font.size'] = 10
        matplotlib.rcParams['axes.titlesize'] = 14
        matplotlib.rcParams['axes.labelsize'] = 12

    # 调用字体设置函数
    setup_matplotlib_chinese()

except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("警告: matplotlib未安装，统计图表功能将不可用")


class StyleManager:
    """样式管理类"""

    @staticmethod
    def get_main_style():
        """获取主窗口样式"""
        return """
        QMainWindow {
            background-color: #f8f9fa;
            color: #212529;
        }

        QMenuBar {
            background-color: #ffffff;
            border-bottom: 1px solid #dee2e6;
            padding: 4px;
        }

        QMenuBar::item {
            background-color: transparent;
            padding: 8px 12px;
            border-radius: 4px;
        }

        QMenuBar::item:selected {
            background-color: #e9ecef;
        }

        QToolBar {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 8px;
            spacing: 8px;
        }

        QPushButton {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 500;
            min-width: 80px;
        }

        QPushButton:hover {
            background-color: #0056b3;
        }

        QPushButton:pressed {
            background-color: #004085;
        }

        QPushButton:disabled {
            background-color: #6c757d;
        }

        QStatusBar {
            background-color: #ffffff;
            border-top: 1px solid #dee2e6;
            padding: 4px;
        }
        """

    @staticmethod
    def get_table_style():
        """获取表格样式"""
        return """
        QTableWidget {
            background-color: #ffffff;
            alternate-background-color: #f8f9fa;
            selection-background-color: #007bff;
            selection-color: white;
            gridline-color: #dee2e6;
            border: 1px solid #dee2e6;
            border-radius: 6px;
        }

        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #f1f3f4;
        }

        QTableWidget::item:hover {
            background-color: #e3f2fd;
        }

        QTableWidget::item:selected {
            background-color: #007bff;
            color: white;
        }

        QHeaderView::section {
            background-color: #495057;
            color: white;
            padding: 10px;
            border: none;
            font-weight: 600;
            text-align: left;
        }

        QHeaderView::section:hover {
            background-color: #343a40;
        }

        QScrollBar:vertical {
            background-color: #f8f9fa;
            width: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:vertical {
            background-color: #6c757d;
            border-radius: 6px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background-color: #495057;
        }

        QScrollBar:horizontal {
            background-color: #f8f9fa;
            height: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:horizontal {
            background-color: #6c757d;
            border-radius: 6px;
            min-width: 20px;
        }

        QScrollBar::handle:horizontal:hover {
            background-color: #495057;
        }
        """

    @staticmethod
    def get_dialog_style():
        """获取对话框样式"""
        return """
        QDialog {
            background-color: #ffffff;
        }

        QLabel {
            color: #495057;
            font-weight: 500;
        }

        QLineEdit, QTextEdit, QComboBox, QDateEdit {
            border: 2px solid #ced4da;
            border-radius: 6px;
            padding: 8px;
            background-color: #ffffff;
            selection-background-color: #007bff;
        }

        QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus {
            border-color: #007bff;
            outline: none;
        }

        QCheckBox {
            spacing: 8px;
        }

        QCheckBox::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #ced4da;
            border-radius: 4px;
            background-color: #ffffff;
        }

        QCheckBox::indicator:checked {
            background-color: #007bff;
            border-color: #007bff;
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
        }

        QDialogButtonBox QPushButton {
            min-width: 100px;
            margin: 4px;
        }
        """


class DatabaseManager:
    """数据库管理类"""

    def __init__(self, db_path: str = "demo_project_orders.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """初始化数据库表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建接单记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_number TEXT UNIQUE NOT NULL,
                delivery_date TEXT NOT NULL,
                requirement_desc TEXT NOT NULL,
                amount REAL NOT NULL,
                need_report BOOLEAN NOT NULL,
                contact_info TEXT NOT NULL,
                remarks TEXT,
                programming_language TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()
        conn.close()

    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        return conn

    def get_next_order_number(self, programming_language: str) -> str:
        """根据编程语言生成下一个入库编号"""
        language_prefixes = {
            'C语言': 'C',
            'C++': 'C',
            'Java': 'J',
            'Python': 'P',
            '多语言项目': 'X',
            'C#': 'CC',
            'Android': 'A'
        }

        base_numbers = {
            'C语言': 1001,
            'C++': 2001,
            'Java': 1001,
            'Python': 1001,
            '多语言项目': 1001,
            'C#': 1001,
            'Android': 1001
        }

        prefix = language_prefixes.get(programming_language, 'X')
        base_number = base_numbers.get(programming_language, 1001)

        conn = self.get_connection()
        cursor = conn.cursor()

        # 查找该语言的最大编号
        cursor.execute('''
            SELECT order_number FROM orders
            WHERE programming_language = ? AND order_number LIKE ?
            ORDER BY order_number DESC LIMIT 1
        ''', (programming_language, f'{prefix}%'))

        result = cursor.fetchone()
        conn.close()

        if result:
            # 提取数字部分并加1
            last_number = result['order_number']
            number_part = ''.join(filter(str.isdigit, last_number))
            if number_part:
                next_number = int(number_part) + 1
            else:
                next_number = base_number
        else:
            next_number = base_number

        return f"{prefix}{next_number}"

    def add_order(self, order_data: Dict) -> bool:
        """添加新的接单记录"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 获取北京时间
            beijing_time = self.get_beijing_time()

            cursor.execute('''
                INSERT INTO orders (order_number, delivery_date, requirement_desc,
                                  amount, need_report, contact_info, remarks,
                                  programming_language, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_data['order_number'],
                order_data['delivery_date'],
                order_data['requirement_desc'],
                order_data['amount'],
                order_data['need_report'],
                order_data['contact_info'],
                order_data['remarks'],
                order_data['programming_language'],
                beijing_time,
                beijing_time
            ))

            conn.commit()
            conn.close()
            return True
        except sqlite3.IntegrityError:
            return False
        except Exception as e:
            print(f"添加记录时出错: {e}")
            return False

    def get_beijing_time(self) -> str:
        """获取北京时间字符串"""
        from datetime import datetime, timezone, timedelta

        # 创建北京时区（UTC+8）
        beijing_tz = timezone(timedelta(hours=8))

        # 获取当前北京时间
        beijing_time = datetime.now(beijing_tz)

        # 格式化为字符串
        return beijing_time.strftime('%Y-%m-%d %H:%M:%S')

    def get_all_orders(self) -> List[Dict]:
        """获取所有接单记录"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM orders ORDER BY created_at DESC
        ''')

        orders = []
        for row in cursor.fetchall():
            orders.append(dict(row))

        conn.close()
        return orders

    def get_filtered_orders(self, language_filter=None, min_amount=None, max_amount=None,
                           start_date=None, end_date=None, report_filter=None) -> List[Dict]:
        """根据筛选条件获取接单记录"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # 构建SQL查询
        where_conditions = []
        params = []

        # 编程语言筛选
        if language_filter:
            where_conditions.append("programming_language = ?")
            params.append(language_filter)

        # 金额范围筛选
        if min_amount is not None:
            where_conditions.append("amount >= ?")
            params.append(min_amount)

        if max_amount is not None:
            where_conditions.append("amount <= ?")
            params.append(max_amount)

        # 日期范围筛选
        if start_date:
            where_conditions.append("delivery_date >= ?")
            params.append(start_date)

        if end_date:
            where_conditions.append("delivery_date <= ?")
            params.append(end_date)

        # 报告要求筛选
        if report_filter == "true":
            where_conditions.append("need_report = 1")
        elif report_filter == "false":
            where_conditions.append("need_report = 0")

        # 构建完整的SQL查询
        sql = "SELECT * FROM orders"
        if where_conditions:
            sql += " WHERE " + " AND ".join(where_conditions)
        sql += " ORDER BY created_at DESC"

        cursor.execute(sql, params)

        orders = []
        for row in cursor.fetchall():
            orders.append(dict(row))

        conn.close()
        return orders

    def update_order(self, order_id: int, order_data: Dict) -> bool:
        """更新接单记录"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 获取北京时间
            beijing_time = self.get_beijing_time()

            cursor.execute('''
                UPDATE orders SET
                    order_number = ?, delivery_date = ?, requirement_desc = ?,
                    amount = ?, need_report = ?, contact_info = ?, remarks = ?,
                    programming_language = ?, updated_at = ?
                WHERE id = ?
            ''', (
                order_data['order_number'],
                order_data['delivery_date'],
                order_data['requirement_desc'],
                order_data['amount'],
                order_data['need_report'],
                order_data['contact_info'],
                order_data['remarks'],
                order_data['programming_language'],
                beijing_time,
                order_id
            ))

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"更新记录时出错: {e}")
            return False

    def delete_orders(self, order_ids: List[int]) -> bool:
        """删除接单记录"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            placeholders = ','.join(['?'] * len(order_ids))
            cursor.execute(f'DELETE FROM orders WHERE id IN ({placeholders})', order_ids)

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"删除记录时出错: {e}")
            return False

    def check_order_number_exists(self, order_number: str, exclude_id: int = None) -> bool:
        """检查入库编号是否已存在"""
        conn = self.get_connection()
        cursor = conn.cursor()

        if exclude_id:
            cursor.execute('SELECT id FROM orders WHERE order_number = ? AND id != ?',
                         (order_number, exclude_id))
        else:
            cursor.execute('SELECT id FROM orders WHERE order_number = ?', (order_number,))

        result = cursor.fetchone()
        conn.close()
        return result is not None


class AddOrderDialog(QDialog):
    """添加接单记录对话框"""

    def __init__(self, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setWindowTitle("📝 添加接单记录")
        self.setModal(True)
        self.resize(600, 500)
        self.setStyleSheet(StyleManager.get_dialog_style())
        self.setup_ui()

    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("添加新的接单记录")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #495057;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # 创建表单布局
        form_layout = QFormLayout()
        form_layout.setSpacing(12)
        form_layout.setLabelAlignment(Qt.AlignRight)

        # 编程语言选择
        self.language_combo = QComboBox()
        self.language_combo.addItems([
            'C语言', 'C++', 'Java', 'Python', '多语言项目', 'C#', 'Android'
        ])
        self.language_combo.setToolTip("选择项目使用的主要编程语言")
        self.language_combo.currentTextChanged.connect(self.on_language_changed)
        form_layout.addRow("编程语言 *:", self.language_combo)

        # 入库编号
        self.order_number_edit = QLineEdit()
        self.order_number_edit.setToolTip("系统自动生成，可手动修改")
        form_layout.addRow("入库编号 *:", self.order_number_edit)

        # 交付日期
        self.delivery_date_edit = QDateEdit()
        self.delivery_date_edit.setDate(QDate.currentDate())
        self.delivery_date_edit.setCalendarPopup(True)
        self.delivery_date_edit.setToolTip("项目预计交付日期")
        form_layout.addRow("交付日期 *:", self.delivery_date_edit)

        # 需求描述
        self.requirement_desc_edit = QTextEdit()
        self.requirement_desc_edit.setMaximumHeight(100)
        self.requirement_desc_edit.setPlaceholderText("请详细描述项目需求...")
        self.requirement_desc_edit.setToolTip("详细描述项目的功能需求")
        form_layout.addRow("需求描述 *:", self.requirement_desc_edit)

        # 金额
        self.amount_edit = QLineEdit()
        self.amount_edit.setPlaceholderText("0.00")
        self.amount_edit.setToolTip("项目总金额（元）")
        form_layout.addRow("金额 *:", self.amount_edit)

        # 是否需要报告
        self.need_report_checkbox = QCheckBox("需要提交项目报告")
        self.need_report_checkbox.setToolTip("是否需要提交项目开发报告")
        form_layout.addRow("报告要求:", self.need_report_checkbox)

        # 联系方式
        self.contact_info_edit = QLineEdit()
        self.contact_info_edit.setPlaceholderText("邮箱、电话或其他联系方式")
        self.contact_info_edit.setToolTip("客户联系方式")
        form_layout.addRow("联系方式 *:", self.contact_info_edit)

        # 备注
        self.remarks_edit = QTextEdit()
        self.remarks_edit.setMaximumHeight(80)
        self.remarks_edit.setPlaceholderText("其他备注信息...")
        self.remarks_edit.setToolTip("其他需要记录的信息")
        form_layout.addRow("备注:", self.remarks_edit)

        layout.addLayout(form_layout)

        # 必填项提示
        required_label = QLabel("* 表示必填项")
        required_label.setStyleSheet("color: #dc3545; font-size: 12px;")
        layout.addWidget(required_label)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.button(QDialogButtonBox.Ok).setText("确定添加")
        button_box.button(QDialogButtonBox.Cancel).setText("取消")
        button_box.button(QDialogButtonBox.Ok).setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: 600;
                min-width: 100px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        button_box.accepted.connect(self.accept_order)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # 初始化入库编号
        self.on_language_changed()

    def on_language_changed(self):
        """编程语言改变时更新入库编号"""
        language = self.language_combo.currentText()
        next_number = self.db_manager.get_next_order_number(language)
        self.order_number_edit.setText(next_number)

    def accept_order(self):
        """确认添加记录"""
        if not self.validate_form():
            return

        order_data = {
            'order_number': self.order_number_edit.text().strip(),
            'delivery_date': self.delivery_date_edit.date().toString('yyyy-MM-dd'),
            'requirement_desc': self.requirement_desc_edit.toPlainText().strip(),
            'amount': float(self.amount_edit.text().strip()),
            'need_report': self.need_report_checkbox.isChecked(),
            'contact_info': self.contact_info_edit.text().strip(),
            'remarks': self.remarks_edit.toPlainText().strip(),
            'programming_language': self.language_combo.currentText()
        }

        if self.db_manager.add_order(order_data):
            self.accept()
        else:
            QMessageBox.warning(self, "错误", "添加记录失败，入库编号可能已存在！")

    def validate_form(self) -> bool:
        """验证表单数据"""
        if not self.order_number_edit.text().strip():
            QMessageBox.warning(self, "验证错误", "入库编号不能为空！")
            return False

        if not self.requirement_desc_edit.toPlainText().strip():
            QMessageBox.warning(self, "验证错误", "需求描述不能为空！")
            return False

        try:
            amount = float(self.amount_edit.text().strip())
            if amount <= 0:
                QMessageBox.warning(self, "验证错误", "金额必须大于0！")
                return False
        except ValueError:
            QMessageBox.warning(self, "验证错误", "请输入有效的金额！")
            return False

        if not self.contact_info_edit.text().strip():
            QMessageBox.warning(self, "验证错误", "联系方式不能为空！")
            return False

        return True


class EditOrderDialog(QDialog):
    """编辑接单记录对话框"""

    def __init__(self, order_data: Dict, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.order_data = order_data.copy()
        self.db_manager = db_manager
        self.setWindowTitle("✏️ 编辑接单记录")
        self.setModal(True)
        self.resize(500, 600)
        self.setStyleSheet(StyleManager.get_dialog_style())
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("✏️ 编辑接单记录")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #495057;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # 表单布局
        form_layout = QFormLayout()
        form_layout.setSpacing(12)

        # 通用输入框样式
        input_style = """
            QLineEdit, QTextEdit, QComboBox, QDateEdit {
                border: 2px solid #ced4da;
                border-radius: 6px;
                padding: 8px;
                background-color: #ffffff;
                selection-background-color: #007bff;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus {
                border-color: #007bff;
                outline: none;
            }
        """

        # 入库编号
        self.order_number_edit = QLineEdit()
        self.order_number_edit.setStyleSheet(input_style)
        form_layout.addRow("入库编号:", self.order_number_edit)

        # 编程语言
        self.language_combo = QComboBox()
        languages = ['C语言', 'C++', 'Java', 'Python', '多语言项目', 'C#', 'Android']
        self.language_combo.addItems(languages)
        self.language_combo.setStyleSheet(input_style)
        form_layout.addRow("编程语言:", self.language_combo)

        # 交付日期
        self.delivery_date_edit = QDateEdit()
        self.delivery_date_edit.setDate(QDate.currentDate())
        self.delivery_date_edit.setCalendarPopup(True)
        self.delivery_date_edit.setStyleSheet(input_style)
        form_layout.addRow("交付日期:", self.delivery_date_edit)

        # 需求描述
        self.requirement_desc_edit = QTextEdit()
        self.requirement_desc_edit.setMaximumHeight(100)
        self.requirement_desc_edit.setStyleSheet(input_style)
        form_layout.addRow("需求描述:", self.requirement_desc_edit)

        # 金额
        self.amount_edit = QLineEdit()
        self.amount_edit.setPlaceholderText("请输入金额（数字）")
        self.amount_edit.setStyleSheet(input_style)
        form_layout.addRow("金额:", self.amount_edit)

        # 是否需要报告
        self.need_report_checkbox = QCheckBox("需要提交项目报告")
        checkbox_style = """
            QCheckBox {
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #ced4da;
                border-radius: 3px;
                background-color: #ffffff;
            }
            QCheckBox::indicator:checked {
                background-color: #007bff;
                border-color: #007bff;
            }
        """
        self.need_report_checkbox.setStyleSheet(checkbox_style)
        form_layout.addRow("报告要求:", self.need_report_checkbox)

        # 联系方式
        self.contact_info_edit = QLineEdit()
        self.contact_info_edit.setPlaceholderText("邮箱、电话或其他联系方式")
        self.contact_info_edit.setStyleSheet(input_style)
        form_layout.addRow("联系方式:", self.contact_info_edit)

        # 备注
        self.remarks_edit = QTextEdit()
        self.remarks_edit.setMaximumHeight(80)
        self.remarks_edit.setPlaceholderText("可选的备注信息")
        self.remarks_edit.setStyleSheet(input_style)
        form_layout.addRow("备注:", self.remarks_edit)

        layout.addLayout(form_layout)

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 500;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #495057;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        save_btn = QPushButton("保存修改")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 500;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        save_btn.clicked.connect(self.save_changes)
        button_layout.addWidget(save_btn)

        layout.addLayout(button_layout)

    def load_data(self):
        """加载现有数据到表单"""
        self.order_number_edit.setText(self.order_data.get('order_number', ''))

        # 设置编程语言
        language = self.order_data.get('programming_language', '')
        index = self.language_combo.findText(language)
        if index >= 0:
            self.language_combo.setCurrentIndex(index)

        # 设置交付日期
        delivery_date = self.order_data.get('delivery_date', '')
        if delivery_date:
            date = QDate.fromString(delivery_date, 'yyyy-MM-dd')
            if date.isValid():
                self.delivery_date_edit.setDate(date)

        self.requirement_desc_edit.setPlainText(self.order_data.get('requirement_desc', ''))
        self.amount_edit.setText(str(self.order_data.get('amount', '')))
        self.need_report_checkbox.setChecked(self.order_data.get('need_report', False))
        self.contact_info_edit.setText(self.order_data.get('contact_info', ''))
        self.remarks_edit.setPlainText(self.order_data.get('remarks', ''))

    def save_changes(self):
        """保存修改"""
        if not self.validate_form():
            return

        # 收集表单数据
        updated_data = {
            'order_number': self.order_number_edit.text().strip(),
            'programming_language': self.language_combo.currentText(),
            'delivery_date': self.delivery_date_edit.date().toString('yyyy-MM-dd'),
            'requirement_desc': self.requirement_desc_edit.toPlainText().strip(),
            'amount': float(self.amount_edit.text().strip()),
            'need_report': self.need_report_checkbox.isChecked(),
            'contact_info': self.contact_info_edit.text().strip(),
            'remarks': self.remarks_edit.toPlainText().strip()
        }

        # 检查入库编号是否已存在（排除当前记录）
        if updated_data['order_number'] != self.order_data['order_number']:
            if self.db_manager.check_order_number_exists(updated_data['order_number']):
                QMessageBox.warning(self, "错误", "该入库编号已存在！")
                return

        # 更新数据库
        if self.db_manager.update_order(self.order_data['id'], updated_data):
            self.accept()
        else:
            QMessageBox.warning(self, "错误", "保存失败！")

    def validate_form(self) -> bool:
        """验证表单数据"""
        if not self.order_number_edit.text().strip():
            QMessageBox.warning(self, "验证错误", "入库编号不能为空！")
            return False

        if not self.requirement_desc_edit.toPlainText().strip():
            QMessageBox.warning(self, "验证错误", "需求描述不能为空！")
            return False

        try:
            amount = float(self.amount_edit.text().strip())
            if amount <= 0:
                QMessageBox.warning(self, "验证错误", "金额必须大于0！")
                return False
        except ValueError:
            QMessageBox.warning(self, "验证错误", "请输入有效的金额！")
            return False

        if not self.contact_info_edit.text().strip():
            QMessageBox.warning(self, "验证错误", "联系方式不能为空！")
            return False

        return True


class ImportPreviewDialog(QDialog):
    """导入预览对话框"""

    def __init__(self, orders: List[Dict], db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.orders = orders
        self.db_manager = db_manager
        self.setWindowTitle("📥 导入预览")
        self.setModal(True)
        self.resize(1000, 600)
        self.setStyleSheet(StyleManager.get_dialog_style())
        self.setup_ui()

    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel(f"📥 导入预览 - 共发现 {len(self.orders)} 条记录")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #495057;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # 说明文本
        info_label = QLabel("请检查以下数据，取消勾选不需要导入的记录：")
        info_label.setStyleSheet("color: #6c757d; margin-bottom: 10px;")
        layout.addWidget(info_label)

        # 创建预览表格
        self.preview_table = QTableWidget()
        self.setup_preview_table()
        layout.addWidget(self.preview_table)

        # 统计信息
        stats_layout = QHBoxLayout()

        # 全选/全不选按钮
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self.select_all)
        stats_layout.addWidget(select_all_btn)

        select_none_btn = QPushButton("全不选")
        select_none_btn.clicked.connect(self.select_none)
        stats_layout.addWidget(select_none_btn)

        stats_layout.addStretch()

        # 冲突检测
        self.conflict_label = QLabel()
        self.update_conflict_info()
        stats_layout.addWidget(self.conflict_label)

        layout.addLayout(stats_layout)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.button(QDialogButtonBox.Ok).setText("确定导入")
        button_box.button(QDialogButtonBox.Cancel).setText("取消")
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def setup_preview_table(self):
        """设置预览表格"""
        columns = ['选择', '入库编号', '编程语言', '交付日期', '需求描述', '金额', '是否需要报告', '联系方式', '备注', '状态']
        self.preview_table.setColumnCount(len(columns))
        self.preview_table.setHorizontalHeaderLabels(columns)
        self.preview_table.setRowCount(len(self.orders))

        # 设置表格属性
        self.preview_table.setAlternatingRowColors(True)
        self.preview_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        # 填充数据
        for row, order in enumerate(self.orders):
            # 选择复选框
            checkbox = QCheckBox()
            checkbox.setChecked(True)
            checkbox.stateChanged.connect(self.update_conflict_info)
            self.preview_table.setCellWidget(row, 0, checkbox)

            # 入库编号
            self.preview_table.setItem(row, 1, QTableWidgetItem(order.get('order_number', '')))

            # 编程语言
            self.preview_table.setItem(row, 2, QTableWidgetItem(order.get('programming_language', '')))

            # 交付日期
            self.preview_table.setItem(row, 3, QTableWidgetItem(order.get('delivery_date', '')))

            # 需求描述
            desc = order.get('requirement_desc', '')
            desc_item = QTableWidgetItem(desc[:50] + "..." if len(desc) > 50 else desc)
            desc_item.setToolTip(desc)
            self.preview_table.setItem(row, 4, desc_item)

            # 金额
            amount_item = QTableWidgetItem(f"¥{order.get('amount', 0):.2f}")
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.preview_table.setItem(row, 5, amount_item)

            # 是否需要报告
            report_item = QTableWidgetItem("是" if order.get('need_report', False) else "否")
            report_item.setTextAlignment(Qt.AlignCenter)
            self.preview_table.setItem(row, 6, report_item)

            # 联系方式
            self.preview_table.setItem(row, 7, QTableWidgetItem(order.get('contact_info', '')))

            # 备注
            remarks = order.get('remarks', '')
            remarks_item = QTableWidgetItem(remarks[:30] + "..." if len(remarks) > 30 else remarks)
            remarks_item.setToolTip(remarks)
            self.preview_table.setItem(row, 8, remarks_item)

            # 状态检查
            status = self.check_record_status(order)
            status_item = QTableWidgetItem(status)
            if "冲突" in status:
                status_item.setForeground(QColor("#dc3545"))
            elif "新记录" in status:
                status_item.setForeground(QColor("#28a745"))
            else:
                status_item.setForeground(QColor("#ffc107"))
            self.preview_table.setItem(row, 9, status_item)

        # 设置列宽
        header = self.preview_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 选择
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 入库编号
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 编程语言
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 交付日期
        header.setSectionResizeMode(4, QHeaderView.Stretch)           # 需求描述
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # 金额
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # 是否需要报告
        header.setSectionResizeMode(7, QHeaderView.Interactive)       # 联系方式
        header.setSectionResizeMode(8, QHeaderView.Interactive)       # 备注
        header.setSectionResizeMode(9, QHeaderView.ResizeToContents)  # 状态

    def check_record_status(self, order: Dict) -> str:
        """检查记录状态"""
        order_number = order.get('order_number', '')
        if not order_number:
            return "⚠️ 缺少编号"

        # 检查是否已存在
        if self.db_manager.check_order_number_exists(order_number):
            return "❌ 编号冲突"

        # 检查必要字段
        if not order.get('requirement_desc', ''):
            return "⚠️ 缺少描述"

        if not order.get('contact_info', ''):
            return "⚠️ 缺少联系方式"

        return "✅ 新记录"

    def select_all(self):
        """全选"""
        for row in range(self.preview_table.rowCount()):
            checkbox = self.preview_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)

    def select_none(self):
        """全不选"""
        for row in range(self.preview_table.rowCount()):
            checkbox = self.preview_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)

    def update_conflict_info(self):
        """更新冲突信息"""
        selected_count = 0
        conflict_count = 0

        for row in range(self.preview_table.rowCount()):
            checkbox = self.preview_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                selected_count += 1
                status_item = self.preview_table.item(row, 9)
                if status_item and "冲突" in status_item.text():
                    conflict_count += 1

        if conflict_count > 0:
            self.conflict_label.setText(f"⚠️ 已选择 {selected_count} 条记录，其中 {conflict_count} 条存在冲突")
            self.conflict_label.setStyleSheet("color: #dc3545; font-weight: bold;")
        else:
            self.conflict_label.setText(f"✅ 已选择 {selected_count} 条记录，无冲突")
            self.conflict_label.setStyleSheet("color: #28a745; font-weight: bold;")

    def get_selected_orders(self) -> List[Dict]:
        """获取选中的记录"""
        selected_orders = []
        for row in range(self.preview_table.rowCount()):
            checkbox = self.preview_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                selected_orders.append(self.orders[row])
        return selected_orders


class StatisticsDialog(QDialog):
    """统计信息对话框"""

    def __init__(self, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setWindowTitle("📊 统计分析")
        self.setModal(True)
        self.resize(900, 700)
        self.setStyleSheet(StyleManager.get_dialog_style())
        self.setup_ui()
        self.load_statistics()

    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("📊 项目统计分析")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #495057;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title_label)

        # 创建选项卡
        self.tab_widget = QWidget()
        tab_layout = QHBoxLayout(self.tab_widget)

        # 左侧统计信息
        self.create_statistics_panel(tab_layout)

        # 右侧图表区域
        if MATPLOTLIB_AVAILABLE:
            self.create_chart_panel(tab_layout)
        else:
            self.create_no_chart_panel(tab_layout)

        layout.addWidget(self.tab_widget)

        # 关闭按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Close)
        button_box.button(QDialogButtonBox.Close).setText("关闭")
        button_box.rejected.connect(self.accept)
        layout.addWidget(button_box)

    def create_statistics_panel(self, parent_layout):
        """创建统计信息面板"""
        stats_widget = QWidget()
        stats_widget.setMaximumWidth(400)
        stats_layout = QVBoxLayout(stats_widget)

        # 总体统计
        overall_group, self.overall_layout = self.create_group_box("📈 总体统计")
        stats_layout.addWidget(overall_group)

        # 按编程语言统计
        language_group, self.language_layout = self.create_group_box("💻 按编程语言统计")
        stats_layout.addWidget(language_group)

        # 按报告要求统计
        report_group, self.report_layout = self.create_group_box("📋 按报告要求统计")
        stats_layout.addWidget(report_group)

        # 按月份统计
        monthly_group, self.monthly_layout = self.create_group_box("📅 按月份统计")
        stats_layout.addWidget(monthly_group)

        parent_layout.addWidget(stats_widget)

    def create_chart_panel(self, parent_layout):
        """创建图表面板"""
        chart_widget = QWidget()
        chart_layout = QVBoxLayout(chart_widget)

        # 图表选择按钮
        button_layout = QHBoxLayout()

        self.lang_chart_btn = QPushButton("编程语言分布")
        self.lang_chart_btn.clicked.connect(lambda: self.show_chart('language'))
        button_layout.addWidget(self.lang_chart_btn)

        self.amount_chart_btn = QPushButton("金额分布")
        self.amount_chart_btn.clicked.connect(lambda: self.show_chart('amount'))
        button_layout.addWidget(self.amount_chart_btn)

        self.monthly_chart_btn = QPushButton("月度趋势")
        self.monthly_chart_btn.clicked.connect(lambda: self.show_chart('monthly'))
        button_layout.addWidget(self.monthly_chart_btn)

        chart_layout.addLayout(button_layout)

        # 图表画布
        self.figure = Figure(figsize=(8, 6))
        self.canvas = FigureCanvas(self.figure)
        chart_layout.addWidget(self.canvas)

        parent_layout.addWidget(chart_widget)

    def create_no_chart_panel(self, parent_layout):
        """创建无图表面板（matplotlib未安装时）"""
        no_chart_widget = QWidget()
        no_chart_layout = QVBoxLayout(no_chart_widget)

        info_label = QLabel("📊 图表功能不可用")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #6c757d;
                padding: 50px;
                border: 2px dashed #dee2e6;
                border-radius: 8px;
            }
        """)

        install_label = QLabel("请安装matplotlib以启用图表功能：\npip install matplotlib")
        install_label.setAlignment(Qt.AlignCenter)
        install_label.setStyleSheet("color: #495057; margin-top: 20px;")

        no_chart_layout.addWidget(info_label)
        no_chart_layout.addWidget(install_label)
        no_chart_layout.addStretch()

        parent_layout.addWidget(no_chart_widget)

    def create_group_box(self, title):
        """创建分组框"""
        group_box = QWidget()
        group_box.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                margin: 5px;
                padding: 10px;
            }
        """)

        # 添加标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #495057;
                font-size: 14px;
                margin-bottom: 10px;
                background-color: transparent;
                border: none;
            }
        """)

        main_layout = QVBoxLayout(group_box)
        main_layout.addWidget(title_label)

        # 创建内容布局
        content_layout = QVBoxLayout()
        main_layout.addLayout(content_layout)

        return group_box, content_layout

    def load_statistics(self):
        """加载统计数据"""
        orders = self.db_manager.get_all_orders()

        if not orders:
            self.show_no_data_message()
            return

        # 总体统计
        self.load_overall_statistics(orders)

        # 按编程语言统计
        self.load_language_statistics(orders)

        # 按报告要求统计
        self.load_report_statistics(orders)

        # 按月份统计
        self.load_monthly_statistics(orders)

    def show_no_data_message(self):
        """显示无数据消息"""
        no_data_label = QLabel("📊 暂无数据")
        no_data_label.setAlignment(Qt.AlignCenter)
        no_data_label.setStyleSheet("color: #6c757d; font-size: 16px; padding: 20px;")
        self.overall_layout.addWidget(no_data_label)

    def load_overall_statistics(self, orders):
        """加载总体统计"""
        total_count = len(orders)
        total_amount = sum(order['amount'] for order in orders)
        avg_amount = total_amount / total_count if total_count > 0 else 0

        # 最高和最低金额
        amounts = [order['amount'] for order in orders]
        max_amount = max(amounts) if amounts else 0
        min_amount = min(amounts) if amounts else 0

        stats_text = f"""📊 项目总数：{total_count} 个
💰 总金额：¥{total_amount:.2f}
📈 平均金额：¥{avg_amount:.2f}
🔝 最高金额：¥{max_amount:.2f}
🔻 最低金额：¥{min_amount:.2f}"""

        stats_label = QLabel(stats_text)
        stats_label.setStyleSheet("color: #495057; line-height: 1.6;")
        self.overall_layout.addWidget(stats_label)

    def load_language_statistics(self, orders):
        """加载编程语言统计"""
        language_stats = {}
        for order in orders:
            lang = order['programming_language']
            if lang not in language_stats:
                language_stats[lang] = {'count': 0, 'amount': 0}
            language_stats[lang]['count'] += 1
            language_stats[lang]['amount'] += order['amount']

        # 按项目数量排序
        sorted_langs = sorted(language_stats.items(), key=lambda x: x[1]['count'], reverse=True)

        for lang, stats in sorted_langs:
            percentage = (stats['count'] / len(orders)) * 100
            lang_text = f"💻 {lang}: {stats['count']}个 (¥{stats['amount']:.2f}) - {percentage:.1f}%"
            lang_label = QLabel(lang_text)
            lang_label.setStyleSheet("color: #495057; margin: 2px 0;")
            self.language_layout.addWidget(lang_label)

    def load_report_statistics(self, orders):
        """加载报告要求统计"""
        need_report = sum(1 for order in orders if order['need_report'])
        no_report = len(orders) - need_report

        need_report_amount = sum(order['amount'] for order in orders if order['need_report'])
        no_report_amount = sum(order['amount'] for order in orders if not order['need_report'])

        report_text = f"""📋 需要报告：{need_report} 个 (¥{need_report_amount:.2f})
📄 无需报告：{no_report} 个 (¥{no_report_amount:.2f})"""

        report_label = QLabel(report_text)
        report_label.setStyleSheet("color: #495057; line-height: 1.6;")
        self.report_layout.addWidget(report_label)

    def load_monthly_statistics(self, orders):
        """加载月份统计"""
        monthly_stats = {}
        for order in orders:
            try:
                date_obj = datetime.strptime(order['delivery_date'], '%Y-%m-%d')
                month_key = date_obj.strftime('%Y-%m')
                if month_key not in monthly_stats:
                    monthly_stats[month_key] = {'count': 0, 'amount': 0}
                monthly_stats[month_key]['count'] += 1
                monthly_stats[month_key]['amount'] += order['amount']
            except ValueError:
                continue

        # 按月份排序
        sorted_months = sorted(monthly_stats.items())

        for month, stats in sorted_months[-6:]:  # 显示最近6个月
            month_text = f"📅 {month}: {stats['count']}个 (¥{stats['amount']:.2f})"
            month_label = QLabel(month_text)
            month_label.setStyleSheet("color: #495057; margin: 2px 0;")
            self.monthly_layout.addWidget(month_label)

    def show_chart(self, chart_type):
        """显示图表"""
        if not MATPLOTLIB_AVAILABLE:
            QMessageBox.information(self, "提示", "图表功能需要安装matplotlib库\n请运行: pip install matplotlib")
            return

        self.figure.clear()
        orders = self.db_manager.get_all_orders()

        if chart_type == 'language':
            self.create_language_chart(orders)
        elif chart_type == 'amount':
            self.create_amount_chart(orders)
        elif chart_type == 'monthly':
            self.create_monthly_chart(orders)

        self.canvas.draw()

    def create_language_chart(self, orders):
        """创建编程语言分布图"""
        if not MATPLOTLIB_AVAILABLE:
            return

        language_stats = {}
        for order in orders:
            lang = order['programming_language']
            language_stats[lang] = language_stats.get(lang, 0) + 1

        if not language_stats:
            return

        ax = self.figure.add_subplot(111)
        languages = list(language_stats.keys())
        counts = list(language_stats.values())

        colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#a29bfe', '#fd79a8']

        # 创建饼图，设置字体
        wedges, texts, autotexts = ax.pie(counts, labels=languages, autopct='%1.1f%%',
                                         colors=colors[:len(languages)], startangle=90)

        # 设置标签字体
        for text in texts:
            text.set_fontsize(10)
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontsize(9)
            autotext.set_weight('bold')

        ax.set_title('编程语言项目分布', fontsize=16, pad=20, weight='bold')

        # 确保图表是圆形
        ax.axis('equal')

    def create_amount_chart(self, orders):
        """创建金额分布图"""
        if not MATPLOTLIB_AVAILABLE:
            return

        language_amounts = {}
        for order in orders:
            lang = order['programming_language']
            language_amounts[lang] = language_amounts.get(lang, 0) + order['amount']

        if not language_amounts:
            return

        ax = self.figure.add_subplot(111)
        languages = list(language_amounts.keys())
        amounts = list(language_amounts.values())

        # 创建渐变色柱状图
        colors = ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1', '#fd7e14', '#20c997']
        bars = ax.bar(languages, amounts, color=colors[:len(languages)], alpha=0.8, edgecolor='white', linewidth=1)

        # 设置标题和标签
        ax.set_title('各编程语言总金额', fontsize=16, pad=20, weight='bold')
        ax.set_ylabel('金额 (元)', fontsize=12, weight='bold')
        ax.set_xlabel('编程语言', fontsize=12, weight='bold')

        # 在柱子上显示数值
        for bar, amount in zip(bars, amounts):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                   f'{amount:.0f}元', ha='center', va='bottom', fontsize=10, weight='bold')

        # 设置网格
        ax.grid(True, alpha=0.3, axis='y')
        ax.set_axisbelow(True)

        # 旋转x轴标签并设置字体
        for tick in ax.get_xticklabels():
            tick.set_rotation(45)
            tick.set_ha('right')
            tick.set_fontsize(10)

        # 设置y轴标签字体
        for tick in ax.get_yticklabels():
            tick.set_fontsize(10)

        # 调整布局
        self.figure.tight_layout()

    def create_monthly_chart(self, orders):
        """创建月度趋势图"""
        if not MATPLOTLIB_AVAILABLE:
            return

        monthly_amounts = {}
        for order in orders:
            try:
                date_obj = datetime.strptime(order['delivery_date'], '%Y-%m-%d')
                month_key = date_obj.strftime('%Y-%m')
                monthly_amounts[month_key] = monthly_amounts.get(month_key, 0) + order['amount']
            except ValueError:
                continue

        if not monthly_amounts:
            return

        ax = self.figure.add_subplot(111)
        months = sorted(monthly_amounts.keys())
        amounts = [monthly_amounts[month] for month in months]

        # 创建线图
        line = ax.plot(months, amounts, marker='o', linewidth=3, markersize=8,
                      color='#28a745', markerfacecolor='#ffffff', markeredgecolor='#28a745',
                      markeredgewidth=2, alpha=0.9)

        # 设置标题和标签
        ax.set_title('月度收入趋势', fontsize=16, pad=20, weight='bold')
        ax.set_ylabel('金额 (元)', fontsize=12, weight='bold')
        ax.set_xlabel('月份', fontsize=12, weight='bold')

        # 在数据点上显示数值
        for month, amount in zip(months, amounts):
            ax.annotate(f'{amount:.0f}元', (month, amount),
                       textcoords="offset points", xytext=(0,10), ha='center',
                       fontsize=9, weight='bold', color='#28a745')

        # 设置网格
        ax.grid(True, alpha=0.3, linestyle='--')
        ax.set_axisbelow(True)

        # 设置背景色
        ax.set_facecolor('#fafafa')

        # 旋转x轴标签并设置字体
        for tick in ax.get_xticklabels():
            tick.set_rotation(45)
            tick.set_ha('right')
            tick.set_fontsize(10)

        # 设置y轴标签字体
        for tick in ax.get_yticklabels():
            tick.set_fontsize(10)

        # 调整布局
        self.figure.tight_layout()


class ProjectOrderManager(QMainWindow):
    """项目接单管理主窗口"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.setWindowTitle("项目接单管理工具")
        self.setGeometry(100, 100, 1200, 700)
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """设置界面"""
        # 应用主窗口样式
        self.setStyleSheet(StyleManager.get_main_style())

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建菜单栏
        self.create_menubar()

        # 工具栏
        self.create_toolbar()

        # 创建筛选面板
        self.create_filter_panel()
        main_layout.addWidget(self.filter_panel)

        # 创建表格
        self.create_table()
        main_layout.addWidget(self.table)

        # 创建状态栏
        self.create_status_bar()

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()

        # 记录数量标签
        self.record_count_label = QLabel("记录数量: 0")
        self.status_bar.addWidget(self.record_count_label)

        # 分隔符
        self.status_bar.addPermanentWidget(QLabel("|"))

        # 总金额标签
        self.total_amount_label = QLabel("总金额: ¥0.00")
        self.status_bar.addPermanentWidget(self.total_amount_label)

        # 分隔符
        self.status_bar.addPermanentWidget(QLabel("|"))

        # 平均金额标签
        self.avg_amount_label = QLabel("平均金额: ¥0.00")
        self.status_bar.addPermanentWidget(self.avg_amount_label)

    def update_status_bar(self):
        """更新状态栏信息"""
        orders = self.db_manager.get_all_orders()
        count = len(orders)
        total_amount = sum(order['amount'] for order in orders)
        avg_amount = total_amount / count if count > 0 else 0

        self.record_count_label.setText(f"记录数量: {count}")
        self.total_amount_label.setText(f"总金额: ¥{total_amount:.2f}")
        self.avg_amount_label.setText(f"平均金额: ¥{avg_amount:.2f}")

    def create_filter_panel(self):
        """创建筛选面板"""
        self.filter_panel = QWidget()
        self.filter_panel.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 15px;
                margin: 5px 0;
            }
        """)

        # 使用垂直布局来更好地组织筛选控件
        main_filter_layout = QVBoxLayout(self.filter_panel)
        main_filter_layout.setSpacing(10)

        # 第一行：编程语言和报告要求
        first_row_layout = QHBoxLayout()
        first_row_layout.setSpacing(20)

        # 第二行：金额范围和日期范围
        second_row_layout = QHBoxLayout()
        second_row_layout.setSpacing(20)

        # 筛选标题
        filter_title = QLabel("🔍 筛选条件:")
        filter_title.setStyleSheet("font-weight: bold; color: #495057; border: none; padding: 0; margin-bottom: 10px;")
        main_filter_layout.addWidget(filter_title)

        # 第一行：编程语言和报告要求
        # 编程语言筛选
        lang_group = QHBoxLayout()
        lang_label = QLabel("编程语言:")
        lang_label.setStyleSheet("color: #495057; border: none; padding: 0; min-width: 80px;")
        lang_group.addWidget(lang_label)

        self.language_filter = QComboBox()
        self.language_filter.addItem("全部", "")
        languages = ['C语言', 'C++', 'Java', 'Python', '多语言项目', 'C#', 'Android']
        for lang in languages:
            self.language_filter.addItem(lang, lang)
        self.language_filter.currentIndexChanged.connect(self.apply_filters)  # 修复：使用currentIndexChanged
        self.language_filter.setStyleSheet("""
            QComboBox {
                min-width: 150px;
                max-width: 200px;
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
        """)
        lang_group.addWidget(self.language_filter)
        lang_group.addStretch()
        first_row_layout.addLayout(lang_group)

        # 报告要求筛选
        report_group = QHBoxLayout()
        report_label = QLabel("报告要求:")
        report_label.setStyleSheet("color: #495057; border: none; padding: 0; min-width: 80px;")
        report_group.addWidget(report_label)

        self.report_filter = QComboBox()
        self.report_filter.addItem("全部", "")
        self.report_filter.addItem("需要报告", "true")
        self.report_filter.addItem("无需报告", "false")
        self.report_filter.currentIndexChanged.connect(self.apply_filters)  # 修复：使用currentIndexChanged
        self.report_filter.setStyleSheet("""
            QComboBox {
                min-width: 120px;
                max-width: 150px;
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
        """)
        report_group.addWidget(self.report_filter)
        report_group.addStretch()
        first_row_layout.addLayout(report_group)

        main_filter_layout.addLayout(first_row_layout)

        # 第二行：金额范围和日期范围
        # 金额范围筛选
        amount_group = QHBoxLayout()
        amount_label = QLabel("金额范围:")
        amount_label.setStyleSheet("color: #495057; border: none; padding: 0; min-width: 80px;")
        amount_group.addWidget(amount_label)

        self.min_amount_edit = QLineEdit()
        self.min_amount_edit.setPlaceholderText("最小金额")
        self.min_amount_edit.setMaximumWidth(120)
        self.min_amount_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #007bff;
            }
        """)
        self.min_amount_edit.textChanged.connect(self.apply_filters)
        amount_group.addWidget(self.min_amount_edit)

        dash_label = QLabel("-")
        dash_label.setStyleSheet("color: #495057; margin: 0 5px;")
        amount_group.addWidget(dash_label)

        self.max_amount_edit = QLineEdit()
        self.max_amount_edit.setPlaceholderText("最大金额")
        self.max_amount_edit.setMaximumWidth(120)
        self.max_amount_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #007bff;
            }
        """)
        self.max_amount_edit.textChanged.connect(self.apply_filters)
        amount_group.addWidget(self.max_amount_edit)
        amount_group.addStretch()
        second_row_layout.addLayout(amount_group)



        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addMonths(-12))
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setMinimumWidth(140)  # 修复：增加最小宽度
        self.start_date_edit.setMaximumWidth(160)
        self.start_date_edit.setStyleSheet("""
            QDateEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QDateEdit:focus {
                border-color: #007bff;
            }
            QDateEdit::drop-down {
                border: none;
                width: 20px;
            }
        """)
        self.start_date_edit.dateChanged.connect(self.apply_filters)

        dash_label2 = QLabel("-")
        dash_label2.setStyleSheet("color: #495057; margin: 0 5px;")

        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate().addMonths(12))
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setMinimumWidth(140)  # 修复：增加最小宽度
        self.end_date_edit.setMaximumWidth(160)
        self.end_date_edit.setStyleSheet("""
            QDateEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QDateEdit:focus {
                border-color: #007bff;
            }
            QDateEdit::drop-down {
                border: none;
                width: 20px;
            }
        """)
        self.end_date_edit.dateChanged.connect(self.apply_filters)

        main_filter_layout.addLayout(second_row_layout)

        # 第三行：清除筛选按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        clear_filter_btn = QPushButton("🗑️ 清除筛选")
        clear_filter_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: 500;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #495057;
            }
        """)
        clear_filter_btn.clicked.connect(self.clear_filters)
        button_layout.addWidget(clear_filter_btn)

        main_filter_layout.addLayout(button_layout)

        # 初始化筛选状态
        self.filtered_orders = []

    def create_menubar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu('文件')

        # 添加记录
        add_action = QAction('添加记录', self)
        add_action.setShortcut('Ctrl+N')
        add_action.triggered.connect(self.add_order)
        file_menu.addAction(add_action)

        file_menu.addSeparator()

        # 导出Markdown
        export_action = QAction('导出Markdown文档', self)
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.export_markdown)
        file_menu.addAction(export_action)

        # 导入Markdown
        import_action = QAction('导入Markdown文档', self)
        import_action.setShortcut('Ctrl+I')
        import_action.triggered.connect(self.import_markdown)
        file_menu.addAction(import_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 编辑菜单
        edit_menu = menubar.addMenu('编辑')

        # 删除记录
        delete_action = QAction('删除记录', self)
        delete_action.setShortcut('Delete')
        delete_action.triggered.connect(self.delete_selected_orders)
        edit_menu.addAction(delete_action)

        # 刷新
        refresh_action = QAction('刷新', self)
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.load_data)
        edit_menu.addAction(refresh_action)

    def create_toolbar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('主工具栏')
        toolbar.setMovable(False)
        toolbar.setFloatable(False)

        # 添加记录按钮
        add_btn = QPushButton('📝 添加记录')
        add_btn.setToolTip('添加新的接单记录 (Ctrl+N)')
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 600;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        add_btn.clicked.connect(self.add_order)
        toolbar.addWidget(add_btn)

        # 添加间距
        toolbar.addSeparator()

        # 删除记录按钮
        delete_btn = QPushButton('🗑️ 删除记录')
        delete_btn.setToolTip('删除选中的记录 (Delete)')
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 600;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)
        delete_btn.clicked.connect(self.delete_selected_orders)
        toolbar.addWidget(delete_btn)

        # 添加间距
        toolbar.addSeparator()

        # 刷新按钮
        refresh_btn = QPushButton('🔄 刷新')
        refresh_btn.setToolTip('刷新数据 (F5)')
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 600;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:pressed {
                background-color: #117a8b;
            }
        """)
        refresh_btn.clicked.connect(self.load_data)
        toolbar.addWidget(refresh_btn)

        # 添加弹性空间，将后续按钮推到右侧
        spacer = QWidget()
        spacer.setSizePolicy(QWidget().sizePolicy().Expanding, QWidget().sizePolicy().Preferred)
        toolbar.addWidget(spacer)

        # 导出按钮
        export_btn = QPushButton('📄 导出')
        export_btn.setToolTip('导出Markdown文档')
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 600;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #e8590c;
            }
            QPushButton:pressed {
                background-color: #d63384;
            }
        """)
        export_btn.clicked.connect(self.export_markdown)
        toolbar.addWidget(export_btn)

        # 导入按钮
        import_btn = QPushButton('📥 导入')
        import_btn.setToolTip('从Markdown文档导入数据')
        import_btn.setStyleSheet("""
            QPushButton {
                background-color: #20c997;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 600;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #1ba085;
            }
            QPushButton:pressed {
                background-color: #198c73;
            }
        """)
        import_btn.clicked.connect(self.import_markdown)
        toolbar.addWidget(import_btn)

        # 统计信息按钮
        stats_btn = QPushButton('📊 统计')
        stats_btn.setToolTip('查看统计信息')
        stats_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 600;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
            QPushButton:pressed {
                background-color: #4e2a8e;
            }
        """)
        stats_btn.clicked.connect(self.show_statistics)
        toolbar.addWidget(stats_btn)

    def create_table(self):
        """创建表格"""
        self.table = QTableWidget()

        # 设置列
        self.columns = ['ID', '入库编号', '交付日期', '需求描述', '金额',
                       '是否需要报告', '联系方式', '备注', '编程语言', '创建时间']
        self.table.setColumnCount(len(self.columns))
        self.table.setHorizontalHeaderLabels(self.columns)

        # 隐藏ID列
        self.table.setColumnHidden(0, True)

        # 设置表格属性
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)
        self.table.setShowGrid(True)
        self.table.setWordWrap(True)

        # 应用表格样式
        self.table.setStyleSheet(StyleManager.get_table_style())

        # 设置响应式列宽
        self.setup_responsive_columns()

        # 双击编辑
        self.table.itemDoubleClicked.connect(self.edit_item)

        # 设置行高
        self.table.verticalHeader().setDefaultSectionSize(40)
        self.table.verticalHeader().hide()

    def setup_responsive_columns(self):
        """设置响应式列宽"""
        header = self.table.horizontalHeader()

        # 列宽配置：(列索引, 最小宽度, 最大宽度, 拉伸模式)
        column_config = {
            1: (100, 150, QHeaderView.ResizeToContents),    # 入库编号
            2: (100, 120, QHeaderView.ResizeToContents),    # 交付日期
            3: (200, 400, QHeaderView.Stretch),             # 需求描述
            4: (80, 120, QHeaderView.ResizeToContents),     # 金额
            5: (80, 120, QHeaderView.ResizeToContents),     # 是否需要报告
            6: (120, 200, QHeaderView.Interactive),         # 联系方式
            7: (100, 300, QHeaderView.Interactive),         # 备注
            8: (80, 120, QHeaderView.ResizeToContents),     # 编程语言
            9: (140, 180, QHeaderView.ResizeToContents),    # 创建时间
        }

        # 应用列宽配置
        for col_index, (min_width, max_width, resize_mode) in column_config.items():
            header.setSectionResizeMode(col_index, resize_mode)
            header.resizeSection(col_index, min_width)
            if resize_mode == QHeaderView.Interactive:
                # 为可交互列设置最小和最大宽度
                header.setMinimumSectionSize(min_width)

        # 设置表头可拖拽调整
        header.setSectionsMovable(False)  # 禁止拖拽移动列
        header.setSectionsClickable(True)  # 允许点击排序

        # 最后一列不自动拉伸，使用固定宽度
        header.setStretchLastSection(False)

    def load_data(self):
        """加载数据到表格"""
        orders = self.db_manager.get_all_orders()
        self.table.setRowCount(len(orders))

        for row, order in enumerate(orders):
            # ID (隐藏列)
            self.table.setItem(row, 0, QTableWidgetItem(str(order['id'])))

            # 入库编号
            order_num_item = QTableWidgetItem(order['order_number'])
            order_num_item.setFont(QFont("Consolas", 9, QFont.Bold))
            self.table.setItem(row, 1, order_num_item)

            # 交付日期
            date_item = QTableWidgetItem(order['delivery_date'])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(row, 2, date_item)

            # 需求描述 - 添加省略号处理
            desc_text = order['requirement_desc']
            desc_display = self.truncate_text(desc_text, 50)
            desc_item = QTableWidgetItem(desc_display)
            desc_item.setToolTip(desc_text)  # 完整文本作为提示
            self.table.setItem(row, 3, desc_item)

            # 金额 - 使用绿色显示
            amount_item = QTableWidgetItem(f"¥{order['amount']:.2f}")
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            amount_item.setForeground(QColor("#28a745"))  # 绿色
            amount_item.setFont(QFont("Arial", 9, QFont.Bold))
            self.table.setItem(row, 4, amount_item)

            # 是否需要报告 - 使用不同颜色标识
            report_text = "是" if order['need_report'] else "否"
            report_item = QTableWidgetItem(report_text)
            report_item.setTextAlignment(Qt.AlignCenter)
            if order['need_report']:
                report_item.setForeground(QColor("#dc3545"))  # 红色表示需要报告
                report_item.setFont(QFont("Arial", 9, QFont.Bold))
            else:
                report_item.setForeground(QColor("#6c757d"))  # 灰色表示不需要报告
            self.table.setItem(row, 5, report_item)

            # 联系方式
            contact_item = QTableWidgetItem(order['contact_info'])
            contact_item.setForeground(QColor("#007bff"))  # 蓝色
            self.table.setItem(row, 6, contact_item)

            # 备注 - 添加省略号处理
            remarks_text = order['remarks'] or ''
            remarks_display = self.truncate_text(remarks_text, 30)
            remarks_item = QTableWidgetItem(remarks_display)
            remarks_item.setToolTip(remarks_text)
            remarks_item.setForeground(QColor("#6c757d"))  # 灰色
            self.table.setItem(row, 7, remarks_item)

            # 编程语言 - 使用不同颜色标识
            lang_item = QTableWidgetItem(order['programming_language'])
            lang_item.setTextAlignment(Qt.AlignCenter)
            lang_item.setFont(QFont("Arial", 8, QFont.Bold))
            # 根据编程语言设置不同颜色
            lang_colors = {
                'C语言': '#ff6b6b',
                'C++': '#4ecdc4',
                'Java': '#45b7d1',
                'Python': '#96ceb4',
                'C#': '#ffeaa7',
                'Android': '#a29bfe',
                '多语言项目': '#fd79a8'
            }
            color = lang_colors.get(order['programming_language'], '#6c757d')
            lang_item.setForeground(QColor(color))
            self.table.setItem(row, 8, lang_item)

            # 创建时间（北京时间）
            created_at = order.get('created_at', '')
            if created_at:
                # 格式化创建时间显示
                try:
                    from datetime import datetime
                    if ' ' in created_at:
                        # 如果包含时间部分，格式化为更友好的显示
                        dt = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                        formatted_time = dt.strftime('%m-%d %H:%M')
                    else:
                        # 只有日期部分
                        formatted_time = created_at
                except:
                    formatted_time = created_at
            else:
                formatted_time = ''

            created_item = QTableWidgetItem(formatted_time)
            created_item.setTextAlignment(Qt.AlignCenter)
            created_item.setForeground(QColor("#6c757d"))  # 灰色
            created_item.setFont(QFont("Arial", 8))
            created_item.setToolTip(f"创建时间（北京时间）: {created_at}")
            self.table.setItem(row, 9, created_item)

        # 更新状态栏
        self.update_status_bar()

        # 保存原始数据用于筛选（按创建时间倒序）
        self.all_orders = orders.copy()
        self.filtered_orders = orders.copy()

        # 应用当前的筛选条件（如果有的话）
        if hasattr(self, 'language_filter') and self.language_filter.currentIndex() > 0:
            self.apply_filters()

    def truncate_text(self, text: str, max_length: int) -> str:
        """截断文本并添加省略号"""
        if not text:
            return ""
        if len(text) <= max_length:
            return text
        return text[:max_length-3] + "..."

    def apply_filters(self):
        """应用筛选条件 - 使用数据库查询"""
        try:
            # 获取筛选条件
            selected_language = self.language_filter.currentData()
            language_filter = selected_language if selected_language else None

            # 金额范围筛选
            min_amount = None
            max_amount = None

            min_amount_text = self.min_amount_edit.text().strip()
            max_amount_text = self.max_amount_edit.text().strip()

            try:
                if min_amount_text:
                    min_amount = float(min_amount_text)
            except ValueError:
                pass

            try:
                if max_amount_text:
                    max_amount = float(max_amount_text)
            except ValueError:
                pass

            # 日期范围筛选
            start_date = self.start_date_edit.date().toString('yyyy-MM-dd')
            end_date = self.end_date_edit.date().toString('yyyy-MM-dd')

            # 报告要求筛选
            report_filter = self.report_filter.currentData()

            # 使用数据库查询获取筛选结果
            filtered_orders = self.db_manager.get_filtered_orders(
                language_filter=language_filter,
                min_amount=min_amount,
                max_amount=max_amount,
                start_date=start_date,
                end_date=end_date,
                report_filter=report_filter
            )

            # 更新显示
            self.filtered_orders = filtered_orders
            self.display_filtered_data(filtered_orders)

        except Exception as e:
            print(f"筛选过程中发生错误: {e}")
            # 如果筛选失败，显示所有数据
            self.load_data()

    def clear_filters(self):
        """清除所有筛选条件"""
        # 重置筛选控件
        self.language_filter.setCurrentIndex(0)
        self.min_amount_edit.clear()
        self.max_amount_edit.clear()
        self.start_date_edit.setDate(QDate.currentDate().addMonths(-12))
        self.end_date_edit.setDate(QDate.currentDate().addMonths(12))
        self.report_filter.setCurrentIndex(0)

        # 显示所有数据
        if hasattr(self, 'all_orders'):
            self.filtered_orders = self.all_orders.copy()
            self.display_filtered_data(self.all_orders)

    def display_filtered_data(self, orders):
        """显示筛选后的数据"""
        self.table.setRowCount(len(orders))

        for row, order in enumerate(orders):
            # ID (隐藏列)
            self.table.setItem(row, 0, QTableWidgetItem(str(order['id'])))

            # 入库编号
            order_num_item = QTableWidgetItem(order['order_number'])
            order_num_item.setFont(QFont("Consolas", 9, QFont.Bold))
            self.table.setItem(row, 1, order_num_item)

            # 交付日期
            date_item = QTableWidgetItem(order['delivery_date'])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(row, 2, date_item)

            # 需求描述 - 添加省略号处理
            desc_text = order['requirement_desc']
            desc_display = self.truncate_text(desc_text, 50)
            desc_item = QTableWidgetItem(desc_display)
            desc_item.setToolTip(desc_text)  # 完整文本作为提示
            self.table.setItem(row, 3, desc_item)

            # 金额 - 使用绿色显示
            amount_item = QTableWidgetItem(f"¥{order['amount']:.2f}")
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            amount_item.setForeground(QColor("#28a745"))  # 绿色
            amount_item.setFont(QFont("Arial", 9, QFont.Bold))
            self.table.setItem(row, 4, amount_item)

            # 是否需要报告 - 使用不同颜色标识
            report_text = "是" if order['need_report'] else "否"
            report_item = QTableWidgetItem(report_text)
            report_item.setTextAlignment(Qt.AlignCenter)
            if order['need_report']:
                report_item.setForeground(QColor("#dc3545"))  # 红色表示需要报告
                report_item.setFont(QFont("Arial", 9, QFont.Bold))
            else:
                report_item.setForeground(QColor("#6c757d"))  # 灰色表示不需要报告
            self.table.setItem(row, 5, report_item)

            # 联系方式
            contact_item = QTableWidgetItem(order['contact_info'])
            contact_item.setForeground(QColor("#007bff"))  # 蓝色
            self.table.setItem(row, 6, contact_item)

            # 备注 - 添加省略号处理
            remarks_text = order['remarks'] or ''
            remarks_display = self.truncate_text(remarks_text, 30)
            remarks_item = QTableWidgetItem(remarks_display)
            remarks_item.setToolTip(remarks_text)
            remarks_item.setForeground(QColor("#6c757d"))  # 灰色
            self.table.setItem(row, 7, remarks_item)

            # 编程语言 - 使用不同颜色标识
            lang_item = QTableWidgetItem(order['programming_language'])
            lang_item.setTextAlignment(Qt.AlignCenter)
            lang_item.setFont(QFont("Arial", 8, QFont.Bold))
            # 根据编程语言设置不同颜色
            lang_colors = {
                'C语言': '#ff6b6b',
                'C++': '#4ecdc4',
                'Java': '#45b7d1',
                'Python': '#96ceb4',
                'C#': '#ffeaa7',
                'Android': '#a29bfe',
                '多语言项目': '#fd79a8'
            }
            color = lang_colors.get(order['programming_language'], '#6c757d')
            lang_item.setForeground(QColor(color))
            self.table.setItem(row, 8, lang_item)

            # 创建时间（北京时间）
            created_at = order.get('created_at', '')
            if created_at:
                # 格式化创建时间显示
                try:
                    from datetime import datetime
                    if ' ' in created_at:
                        # 如果包含时间部分，格式化为更友好的显示
                        dt = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                        formatted_time = dt.strftime('%m-%d %H:%M')
                    else:
                        # 只有日期部分
                        formatted_time = created_at
                except:
                    formatted_time = created_at
            else:
                formatted_time = ''

            created_item = QTableWidgetItem(formatted_time)
            created_item.setTextAlignment(Qt.AlignCenter)
            created_item.setForeground(QColor("#6c757d"))  # 灰色
            created_item.setFont(QFont("Arial", 8))
            created_item.setToolTip(f"创建时间（北京时间）: {created_at}")
            self.table.setItem(row, 9, created_item)

        # 更新状态栏（使用筛选后的数据）
        self.update_filtered_status_bar(orders)

    def update_filtered_status_bar(self, orders):
        """更新筛选后的状态栏信息"""
        count = len(orders)
        total_amount = sum(order['amount'] for order in orders)
        avg_amount = total_amount / count if count > 0 else 0

        self.record_count_label.setText(f"记录数量: {count}")
        self.total_amount_label.setText(f"总金额: ¥{total_amount:.2f}")
        self.avg_amount_label.setText(f"平均金额: ¥{avg_amount:.2f}")

    def update_filtered_status_bar(self, filtered_orders):
        """更新筛选后的状态栏信息"""
        count = len(filtered_orders)
        total_amount = sum(order['amount'] for order in filtered_orders)
        avg_amount = total_amount / count if count > 0 else 0

        # 显示筛选信息
        total_count = len(self.all_orders) if hasattr(self, 'all_orders') else 0
        if count < total_count:
            self.record_count_label.setText(f"记录数量: {count}/{total_count} (已筛选)")
        else:
            self.record_count_label.setText(f"记录数量: {count}")

        self.total_amount_label.setText(f"总金额: ¥{total_amount:.2f}")
        self.avg_amount_label.setText(f"平均金额: ¥{avg_amount:.2f}")

    def add_order(self):
        """添加新记录"""
        dialog = AddOrderDialog(self.db_manager, self)
        if dialog.exec_() == QDialog.Accepted:
            # 显示加载提示
            self.status_bar.showMessage("正在添加记录...", 2000)
            self.load_data()
            # 重新应用筛选
            self.apply_filters()

            # 显示成功消息
            msg = QMessageBox(self)
            msg.setIcon(QMessageBox.Information)
            msg.setWindowTitle("操作成功")
            msg.setText("✅ 记录添加成功！")
            msg.setStyleSheet("""
                QMessageBox {
                    background-color: #ffffff;
                }
                QMessageBox QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background-color: #218838;
                }
            """)
            msg.exec_()

    def delete_selected_orders(self):
        """删除选中的记录"""
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择要删除的记录！")
            return

        # 确认删除
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Warning)
        msg.setWindowTitle("确认删除")
        msg.setText(f"⚠️ 确定要删除选中的 {len(selected_rows)} 条记录吗？")
        msg.setInformativeText("此操作不可撤销，请谨慎操作。")
        msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        msg.setDefaultButton(QMessageBox.No)
        msg.button(QMessageBox.Yes).setText("确定删除")
        msg.button(QMessageBox.No).setText("取消")
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #ffffff;
            }
            QMessageBox QPushButton {
                min-width: 80px;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: 500;
            }
            QMessageBox QPushButton[text="确定删除"] {
                background-color: #dc3545;
                color: white;
                border: none;
            }
            QMessageBox QPushButton[text="确定删除"]:hover {
                background-color: #c82333;
            }
            QMessageBox QPushButton[text="取消"] {
                background-color: #6c757d;
                color: white;
                border: none;
            }
            QMessageBox QPushButton[text="取消"]:hover {
                background-color: #5a6268;
            }
        """)

        if msg.exec_() == QMessageBox.Yes:
            # 显示删除进度
            self.status_bar.showMessage("正在删除记录...", 2000)

            # 获取要删除的ID
            order_ids = []
            for row in selected_rows:
                id_item = self.table.item(row, 0)
                if id_item:
                    order_ids.append(int(id_item.text()))

            if self.db_manager.delete_orders(order_ids):
                self.load_data()
                # 重新应用筛选
                self.apply_filters()

                # 显示成功消息
                success_msg = QMessageBox(self)
                success_msg.setIcon(QMessageBox.Information)
                success_msg.setWindowTitle("操作成功")
                success_msg.setText("✅ 记录删除成功！")
                success_msg.setStyleSheet("""
                    QMessageBox QPushButton {
                        background-color: #28a745;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        min-width: 80px;
                    }
                """)
                success_msg.exec_()
            else:
                # 显示错误消息
                error_msg = QMessageBox(self)
                error_msg.setIcon(QMessageBox.Critical)
                error_msg.setWindowTitle("操作失败")
                error_msg.setText("❌ 删除记录失败！")
                error_msg.setInformativeText("请检查数据库连接或联系技术支持。")
                error_msg.setStyleSheet("""
                    QMessageBox QPushButton {
                        background-color: #dc3545;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        min-width: 80px;
                    }
                """)
                error_msg.exec_()

    def edit_item(self, item):
        """编辑表格项 - 使用编辑对话框"""
        if item.column() == 0:  # ID列不可编辑
            return

        row = item.row()

        # 获取当前记录的ID
        id_item = self.table.item(row, 0)
        if not id_item:
            return

        order_id = int(id_item.text())

        # 获取当前记录的完整数据
        orders = self.db_manager.get_all_orders()
        current_order = None
        for order in orders:
            if order['id'] == order_id:
                current_order = order
                break

        if not current_order:
            QMessageBox.warning(self, "错误", "无法找到该记录！")
            return

        # 打开编辑对话框
        dialog = EditOrderDialog(current_order, self.db_manager, self)
        if dialog.exec_() == QDialog.Accepted:
            # 刷新数据显示
            self.load_data()
            self.apply_filters()  # 重新应用筛选条件

    def update_order_field(self, order_id: int, field_name: str, new_value) -> bool:
        """更新订单字段"""
        # 获取当前记录的所有数据
        orders = self.db_manager.get_all_orders()
        current_order = None

        for order in orders:
            if order['id'] == order_id:
                current_order = order
                break

        if not current_order:
            return False

        # 更新字段值
        current_order[field_name] = new_value

        # 保存到数据库
        return self.db_manager.update_order(order_id, current_order)

    def export_markdown(self):
        """导出Markdown文档"""
        # 获取所有数据
        orders = self.db_manager.get_all_orders()
        if not orders:
            QMessageBox.information(self, "提示", "没有数据可以导出！")
            return

        # 选择保存位置
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出Markdown文档",
            f"项目接单记录_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
            "Markdown文件 (*.md);;所有文件 (*)"
        )

        if not file_path:
            return

        try:
            # 按编程语言分组
            language_groups = {}
            for order in orders:
                lang = order['programming_language']
                if lang not in language_groups:
                    language_groups[lang] = []
                language_groups[lang].append(order)

            # 生成Markdown内容
            markdown_content = self.generate_markdown_content(language_groups)

            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            # 显示成功消息
            msg = QMessageBox(self)
            msg.setIcon(QMessageBox.Information)
            msg.setWindowTitle("导出成功")
            msg.setText("✅ Markdown文档导出成功！")
            msg.setInformativeText(f"文件已保存到：{file_path}")
            msg.setStyleSheet("""
                QMessageBox QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    min-width: 80px;
                }
            """)
            msg.exec_()

        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"导出过程中发生错误：\n{str(e)}")

    def generate_markdown_content(self, language_groups):
        """生成Markdown内容"""
        content = []

        # 添加标题和说明
        content.append("# 项目接单记录")
        content.append("")
        content.append(f"**导出时间：** {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}")
        content.append("")

        # 统计信息
        total_orders = sum(len(orders) for orders in language_groups.values())
        total_amount = sum(order['amount'] for orders in language_groups.values() for order in orders)
        content.append("## 📊 统计概览")
        content.append("")
        content.append(f"- **总项目数：** {total_orders}")
        content.append(f"- **总金额：** ¥{total_amount:.2f}")
        content.append(f"- **平均金额：** ¥{total_amount/total_orders:.2f}")
        content.append(f"- **编程语言种类：** {len(language_groups)}")
        content.append("")

        # 按编程语言分组显示
        for language, orders in sorted(language_groups.items()):
            content.append(f"## {language}")
            content.append("")

            # 语言统计
            lang_total = sum(order['amount'] for order in orders)
            content.append(f"**项目数量：** {len(orders)} | **总金额：** ¥{lang_total:.2f}")
            content.append("")

            # 表格头
            content.append("| 入库编号 | 交付日期 | 需求描述 | 金额 | 是否需要报告 | 联系方式 | 备注 |")
            content.append("|---------|---------|---------|------|-------------|---------|------|")

            # 表格数据
            for order in sorted(orders, key=lambda x: x['order_number']):
                desc = order['requirement_desc'].replace('|', '\\|').replace('\n', ' ')[:50]
                if len(order['requirement_desc']) > 50:
                    desc += "..."

                remarks = (order['remarks'] or '').replace('|', '\\|').replace('\n', ' ')[:30]
                if order['remarks'] and len(order['remarks']) > 30:
                    remarks += "..."

                report_text = "是" if order['need_report'] else "否"

                content.append(f"| {order['order_number']} | {order['delivery_date']} | {desc} | ¥{order['amount']:.2f} | {report_text} | {order['contact_info']} | {remarks} |")

            content.append("")

        # 添加页脚
        content.append("---")
        content.append("")
        content.append("*此文档由项目接单管理工具自动生成*")

        return '\n'.join(content)

    def import_markdown(self):
        """导入Markdown文档"""
        # 选择要导入的文件
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择要导入的Markdown文档",
            "",
            "Markdown文件 (*.md);;文本文件 (*.txt);;所有文件 (*)"
        )

        if not file_path:
            return

        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析Markdown内容
            imported_orders = self.parse_markdown_content(content)

            if not imported_orders:
                QMessageBox.warning(self, "导入失败", "未能从文档中解析出有效的项目记录！")
                return

            # 显示导入预览对话框
            dialog = ImportPreviewDialog(imported_orders, self.db_manager, self)
            if dialog.exec_() == QDialog.Accepted:
                # 执行导入
                success_count, error_count = self.execute_import(dialog.get_selected_orders())

                # 显示导入结果
                if success_count > 0:
                    self.load_data()
                    self.apply_filters()

                msg = QMessageBox(self)
                msg.setIcon(QMessageBox.Information)
                msg.setWindowTitle("导入完成")
                msg.setText(f"✅ 导入完成！")
                msg.setInformativeText(f"成功导入: {success_count} 条记录\n失败: {error_count} 条记录")
                msg.exec_()

        except Exception as e:
            QMessageBox.critical(self, "导入错误", f"导入过程中发生错误：\n{str(e)}")

    def parse_markdown_content(self, content: str) -> List[Dict]:
        """解析Markdown内容，支持多种表格格式"""

        orders = []
        lines = content.split('\n')
        current_language = None

        # 定义可能的列名映射
        column_mappings = {
            '入库编号': 'order_number',
            '编号': 'order_number',
            '项目编号': 'order_number',
            '交付日期': 'delivery_date',
            '日期': 'delivery_date',
            '完成日期': 'delivery_date',
            '需求描述': 'requirement_desc',
            '描述': 'requirement_desc',
            '项目描述': 'requirement_desc',
            '需求': 'requirement_desc',
            '金额': 'amount',
            '价格': 'amount',
            '费用': 'amount',
            '是否需要报告': 'need_report',
            '需要报告': 'need_report',
            '报告': 'need_report',
            '联系方式': 'contact_info',
            '联系人': 'contact_info',
            '客户': 'contact_info',
            '备注': 'remarks',
            '说明': 'remarks',
            '注释': 'remarks'
        }

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # 检测编程语言标题
            if line.startswith('#') and not line.startswith('##'):
                # 提取语言名称
                lang_match = re.search(r'#\s*(.+)', line)
                if lang_match:
                    potential_lang = lang_match.group(1).strip()
                    # 检查是否是已知的编程语言
                    known_languages = ['C语言', 'C++', 'Java', 'Python', '多语言项目', 'C#', 'Android']
                    if potential_lang in known_languages:
                        current_language = potential_lang
                    elif any(lang in potential_lang for lang in known_languages):
                        # 模糊匹配
                        for lang in known_languages:
                            if lang in potential_lang:
                                current_language = lang
                                break

            # 检测表格头
            elif '|' in line and ('编号' in line or '日期' in line or '金额' in line):
                # 解析表格
                table_orders = self.parse_table_from_lines(lines[i:], current_language, column_mappings)
                orders.extend(table_orders)

                # 跳过已处理的表格行
                j = i + 1
                while j < len(lines) and ('|' in lines[j] or lines[j].strip() == ''):
                    j += 1
                i = j - 1

            i += 1

        return orders

    def parse_table_from_lines(self, lines: List[str], language: str, column_mappings: Dict) -> List[Dict]:
        """从表格行中解析数据"""
        orders = []
        header_line = None
        separator_line = None

        # 找到表头和分隔符
        for i, line in enumerate(lines):
            if '|' in line and header_line is None:
                header_line = i
            elif '|' in line and '-' in line and header_line is not None:
                separator_line = i
                break

        if header_line is None:
            return orders

        # 解析表头
        header_cells = [cell.strip() for cell in lines[header_line].split('|') if cell.strip()]

        # 映射列名到字段名
        field_mapping = {}
        for i, header in enumerate(header_cells):
            for col_name, field_name in column_mappings.items():
                if col_name in header:
                    field_mapping[i] = field_name
                    break

        # 解析数据行
        start_line = separator_line + 1 if separator_line else header_line + 1
        for line in lines[start_line:]:
            if not line.strip() or '|' not in line:
                break

            cells = [cell.strip() for cell in line.split('|') if cell.strip()]
            if len(cells) < 3:  # 至少需要3列数据
                continue

            order = {
                'programming_language': language or '未知',
                'order_number': '',
                'delivery_date': '',
                'requirement_desc': '',
                'amount': 0.0,
                'need_report': False,
                'contact_info': '',
                'remarks': ''
            }

            # 填充数据
            for i, cell in enumerate(cells):
                if i in field_mapping:
                    field_name = field_mapping[i]

                    if field_name == 'amount':
                        # 解析金额 - 增强版本
                        amount_str = self.parse_amount_string(cell)
                        order[field_name] = amount_str

                    elif field_name == 'need_report':
                        # 解析布尔值
                        order[field_name] = cell in ['是', 'True', 'true', '1', '需要']

                    elif field_name == 'delivery_date':
                        # 验证日期格式
                        try:
                            datetime.strptime(cell, '%Y-%m-%d')
                            order[field_name] = cell
                        except ValueError:
                            # 尝试其他日期格式
                            try:
                                date_obj = datetime.strptime(cell, '%Y/%m/%d')
                                order[field_name] = date_obj.strftime('%Y-%m-%d')
                            except ValueError:
                                order[field_name] = datetime.now().strftime('%Y-%m-%d')

                    else:
                        order[field_name] = cell

            # 验证必要字段
            if order['order_number'] and order['requirement_desc']:
                orders.append(order)

        return orders

    def execute_import(self, orders: List[Dict]) -> Tuple[int, int]:
        """执行导入操作"""
        success_count = 0
        error_count = 0

        for order in orders:
            try:
                if self.db_manager.add_order(order):
                    success_count += 1
                else:
                    error_count += 1
            except Exception:
                error_count += 1

        return success_count, error_count

    def parse_amount_string(self, amount_str: str) -> float:
        """解析金额字符串，支持多种格式"""
        if not amount_str or not amount_str.strip():
            return 0.0

        # 移除空格
        amount_str = amount_str.strip()

        # 移除常见的货币符号和分隔符
        # 支持：¥1500, $1500, 1,500.00, 1500.00, 1500, ￥1500等
        cleaned_str = re.sub(r'[¥￥$€£,，、\s]', '', amount_str)

        # 处理可能的中文数字（简单处理）
        chinese_numbers = {
            '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
            '六': '6', '七': '7', '八': '8', '九': '9', '零': '0',
            '十': '10', '百': '100', '千': '1000', '万': '10000'
        }

        # 如果包含中文数字，尝试简单转换
        for chinese, arabic in chinese_numbers.items():
            if chinese in cleaned_str:
                cleaned_str = cleaned_str.replace(chinese, arabic)

        # 尝试提取数字
        # 匹配整数或小数
        number_pattern = r'(\d+(?:\.\d+)?)'
        matches = re.findall(number_pattern, cleaned_str)

        if matches:
            try:
                # 取第一个匹配的数字
                return float(matches[0])
            except ValueError:
                pass

        # 如果以上都失败，尝试直接转换
        try:
            return float(cleaned_str)
        except ValueError:
            print(f"无法解析金额: '{amount_str}' -> '{cleaned_str}'")
            return 0.0

    def show_statistics(self):
        """显示统计信息"""
        dialog = StatisticsDialog(self.db_manager, self)
        dialog.exec_()

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 如果有未保存的更改，可以在这里添加确认对话框
        # 目前直接接受关闭事件
        event.accept()


def main():
    """主程序入口"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("项目接单管理工具")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("项目管理工具")

    # 设置全局字体
    if sys.platform == 'win32':
        font = QFont("Microsoft YaHei UI", 9)
    elif sys.platform == 'darwin':
        font = QFont("SF Pro Display", 9)
    else:
        font = QFont("Ubuntu", 9)

    app.setFont(font)

    # 设置全局样式表
    app.setStyleSheet("""
        * {
            font-family: "Microsoft YaHei UI", "Segoe UI", "Arial", sans-serif;
        }

        QToolTip {
            background-color: #2c3e50;
            color: white;
            border: 1px solid #34495e;
            border-radius: 4px;
            padding: 8px;
            font-size: 12px;
        }

        QMessageBox {
            background-color: #ffffff;
        }

        QMessageBox QPushButton {
            min-width: 80px;
            padding: 6px 12px;
        }
    """)

    # 创建主窗口
    window = ProjectOrderManager()

    # 设置窗口图标（如果有的话）
    # window.setWindowIcon(QIcon("icon.png"))

    # 居中显示窗口
    screen = app.primaryScreen().geometry()
    window_size = window.geometry()
    x = (screen.width() - window_size.width()) // 2
    y = (screen.height() - window_size.height()) // 2
    window.move(x, y)

    window.show()

    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
