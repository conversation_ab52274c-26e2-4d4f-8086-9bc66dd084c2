#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目接单管理工具
使用PyQt5开发的项目管理系统
"""

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QFont

from config import APP_NAME, APP_VERSION, ORGANIZATION_NAME, get_default_font
from main_window import ProjectOrderManager


def main():
    """主程序入口"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName(APP_NAME)
    app.setApplicationVersion(APP_VERSION)
    app.setOrganizationName(ORGANIZATION_NAME)

    # 设置全局字体
    if sys.platform == 'win32':
        font = QFont(get_default_font(), 9)
        app.setFont(font)

    # 创建主窗口
    window = ProjectOrderManager()
    window.show()

    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
