#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入导出功能模块
包含Markdown导入导出相关的功能
"""

from datetime import datetime
from typing import List, Dict, Tuple
from PyQt5.QtWidgets import QMessageBox, QFileDialog, QDialog
from config import EXPORT_FILENAME_FORMAT
from utils import generate_markdown_content, parse_markdown_content, execute_import
from dialogs import ImportPreviewDialog


class ImportExportMixin:
    """导入导出功能混入类"""

    def export_markdown(self):
        """导出Markdown文档"""
        # 获取所有数据
        orders = self.db_manager.get_all_orders()
        if not orders:
            QMessageBox.information(self, "提示", "没有数据可以导出！")
            return

        # 选择保存位置
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出Markdown文档",
            EXPORT_FILENAME_FORMAT.format(datetime.now().strftime('%Y%m%d_%H%M%S')),
            "Markdown文件 (*.md);;所有文件 (*)"
        )

        if not file_path:
            return

        try:
            # 按编程语言分组
            language_groups = {}
            for order in orders:
                lang = order['programming_language']
                if lang not in language_groups:
                    language_groups[lang] = []
                language_groups[lang].append(order)

            # 生成Markdown内容
            markdown_content = generate_markdown_content(language_groups)

            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            # 显示成功消息
            msg = QMessageBox(self)
            msg.setIcon(QMessageBox.Information)
            msg.setWindowTitle("导出成功")
            msg.setText("✅ Markdown文档导出成功！")
            msg.setInformativeText(f"文件已保存到：{file_path}")
            msg.setStyleSheet("""
                QMessageBox QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    min-width: 80px;
                }
            """)
            msg.exec_()

        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"导出过程中发生错误：\n{str(e)}")

    def import_markdown(self):
        """导入Markdown文档"""
        # 选择要导入的文件
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择要导入的Markdown文档",
            "",
            "Markdown文件 (*.md);;文本文件 (*.txt);;所有文件 (*)"
        )

        if not file_path:
            return

        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析Markdown内容
            imported_orders = parse_markdown_content(content)

            if not imported_orders:
                QMessageBox.warning(self, "导入失败", "未能从文档中解析出有效的项目记录！")
                return

            # 显示导入预览对话框
            dialog = ImportPreviewDialog(imported_orders, self.db_manager, self)
            if dialog.exec_() == QDialog.Accepted:
                # 执行导入
                success_count, error_count = execute_import(self.db_manager, dialog.get_selected_orders())

                # 显示导入结果
                if success_count > 0:
                    self.load_data()
                    self.apply_filters()

                msg = QMessageBox(self)
                msg.setIcon(QMessageBox.Information)
                msg.setWindowTitle("导入完成")
                msg.setText(f"✅ 导入完成！")
                msg.setInformativeText(f"成功导入: {success_count} 条记录\n失败: {error_count} 条记录")
                msg.exec_()

        except Exception as e:
            QMessageBox.critical(self, "导入错误", f"导入过程中发生错误：\n{str(e)}")

    def delete_selected_orders(self):
        """删除选中的记录"""
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择要删除的记录！")
            return

        # 确认删除
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Warning)
        msg.setWindowTitle("确认删除")
        msg.setText(f"⚠️ 确定要删除选中的 {len(selected_rows)} 条记录吗？")
        msg.setInformativeText("此操作不可撤销，请谨慎操作。")
        msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        msg.setDefaultButton(QMessageBox.No)
        msg.button(QMessageBox.Yes).setText("确定删除")
        msg.button(QMessageBox.No).setText("取消")
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #ffffff;
            }
            QMessageBox QPushButton {
                min-width: 80px;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: 500;
            }
            QMessageBox QPushButton[text="确定删除"] {
                background-color: #dc3545;
                color: white;
                border: none;
            }
            QMessageBox QPushButton[text="确定删除"]:hover {
                background-color: #c82333;
            }
            QMessageBox QPushButton[text="取消"] {
                background-color: #6c757d;
                color: white;
                border: none;
            }
            QMessageBox QPushButton[text="取消"]:hover {
                background-color: #5a6268;
            }
        """)

        if msg.exec_() == QMessageBox.Yes:
            # 显示删除进度
            self.status_bar.showMessage("正在删除记录...", 2000)

            # 获取要删除的ID
            order_ids = []
            for row in selected_rows:
                id_item = self.table.item(row, 0)
                if id_item:
                    order_ids.append(int(id_item.text()))

            if self.db_manager.delete_orders(order_ids):
                self.load_data()
                # 重新应用筛选
                self.apply_filters()

                # 显示成功消息
                success_msg = QMessageBox(self)
                success_msg.setIcon(QMessageBox.Information)
                success_msg.setWindowTitle("操作成功")
                success_msg.setText("✅ 记录删除成功！")
                success_msg.setStyleSheet("""
                    QMessageBox QPushButton {
                        background-color: #28a745;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        min-width: 80px;
                    }
                """)
                success_msg.exec_()
            else:
                # 显示错误消息
                error_msg = QMessageBox(self)
                error_msg.setIcon(QMessageBox.Critical)
                error_msg.setWindowTitle("操作失败")
                error_msg.setText("❌ 删除记录失败！")
                error_msg.setInformativeText("请检查数据库连接或联系技术支持。")
                error_msg.setStyleSheet("""
                    QMessageBox QPushButton {
                        background-color: #dc3545;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        min-width: 80px;
                    }
                """)
                error_msg.exec_()
