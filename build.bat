@echo off
chcp 65001 >nul
echo ========================================
echo 项目接单管理工具 - PyInstaller 打包脚本
echo ========================================
echo.

echo [1/4] 清理之前的构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"
echo ✓ 清理完成

echo.
echo [2/4] 检查依赖...
python -c "import PyQt5; print('✓ PyQt5 已安装')" 2>nul || (echo ✗ PyQt5 未安装 && pause && exit /b 1)
python -c "import matplotlib; print('✓ matplotlib 已安装')" 2>nul || (echo ✗ matplotlib 未安装 && pause && exit /b 1)
python -c "import sqlite3; print('✓ sqlite3 已安装')" 2>nul || (echo ✗ sqlite3 未安装 && pause && exit /b 1)

echo.
echo [3/4] 开始打包...
echo 这可能需要几分钟时间，请耐心等待...
pyinstaller build_exe.spec --clean --noconfirm

echo.
echo [4/4] 检查打包结果...
if exist "dist\项目接单管理工具.exe" (
    echo ✓ 打包成功！
    echo.
    echo 可执行文件位置: dist\项目接单管理工具.exe
    echo 文件大小: 
    for %%A in ("dist\项目接单管理工具.exe") do echo   %%~zA 字节
    echo.
    echo 是否立即运行测试？(Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo 启动程序...
        start "" "dist\项目接单管理工具.exe"
    )
) else (
    echo ✗ 打包失败！
    echo 请检查上面的错误信息
    pause
    exit /b 1
)

echo.
echo ========================================
echo 打包完成！
echo ========================================
pause
