# -*- mode: python ; coding: utf-8 -*-
"""
项目接单管理工具 PyInstaller 配置文件
"""

import os
import sys

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(__file__))

a = Analysis(
    ['main.py'],
    pathex=[current_dir],
    binaries=[],
    datas=[
        # 包含所有Python模块文件
        ('config.py', '.'),
        ('database.py', '.'),
        ('dialogs.py', '.'),
        ('import_export.py', '.'),
        ('main_window.py', '.'),
        ('statistics_dialog.py', '.'),
        ('styles.py', '.'),
        ('utils.py', '.'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'matplotlib',
        'matplotlib.backends.backend_qt5agg',
        'matplotlib.figure',
        'matplotlib.pyplot',
        'matplotlib.dates',
        'sqlite3',
        'datetime',
        'typing',
        're',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'unittest',
        'test',
        'distutils',
    ],
    noarchive=False,
    optimize=0,
)

# 过滤掉不需要的文件
a.datas = [x for x in a.datas if not x[0].startswith('tcl')]
a.datas = [x for x in a.datas if not x[0].startswith('tk')]

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='项目接单管理工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',  # 版本信息文件（可选）
    # icon='icon.ico',  # 图标文件（如果有的话）
)
