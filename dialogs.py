#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对话框模块
包含所有对话框类的定义
"""

import re
from datetime import datetime
from typing import List, Dict, Tuple
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, 
                             QLineEdit, QComboBox, QDateEdit, QCheckBox, QTextEdit,
                             QMessageBox, QDialogButtonBox, QPushButton, QLabel,
                             QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
                             QWidget)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QColor

from config import PROGRAMMING_LANGUAGES, MATPLOTLIB_AVAILABLE, COLUMN_MAPPINGS, CHINESE_NUMBERS
from styles import StyleManager
from database import DatabaseManager

if MATPLOTLIB_AVAILABLE:
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure


class AddOrderDialog(QDialog):
    """添加接单记录对话框"""

    def __init__(self, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setWindowTitle("📝 添加接单记录")
        self.setModal(True)
        self.resize(600, 500)
        self.setStyleSheet(StyleManager.get_dialog_style())
        self.setup_ui()

    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("添加新的接单记录")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #495057;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # 创建表单布局
        form_layout = QFormLayout()
        form_layout.setSpacing(12)
        form_layout.setLabelAlignment(Qt.AlignRight)

        # 编程语言选择
        self.language_combo = QComboBox()
        self.language_combo.addItems(PROGRAMMING_LANGUAGES)
        self.language_combo.setToolTip("选择项目使用的主要编程语言")
        self.language_combo.currentTextChanged.connect(self.on_language_changed)
        form_layout.addRow("编程语言 *:", self.language_combo)

        # 入库编号
        self.order_number_edit = QLineEdit()
        self.order_number_edit.setToolTip("系统自动生成，可手动修改")
        form_layout.addRow("入库编号 *:", self.order_number_edit)

        # 交付日期
        self.delivery_date_edit = QDateEdit()
        self.delivery_date_edit.setDate(QDate.currentDate())
        self.delivery_date_edit.setCalendarPopup(True)
        self.delivery_date_edit.setToolTip("项目预计交付日期")
        form_layout.addRow("交付日期 *:", self.delivery_date_edit)

        # 需求描述
        self.requirement_desc_edit = QTextEdit()
        self.requirement_desc_edit.setMaximumHeight(100)
        self.requirement_desc_edit.setPlaceholderText("请详细描述项目需求...")
        self.requirement_desc_edit.setToolTip("详细描述项目的功能需求")
        form_layout.addRow("需求描述 *:", self.requirement_desc_edit)

        # 金额
        self.amount_edit = QLineEdit()
        self.amount_edit.setPlaceholderText("0.00")
        self.amount_edit.setToolTip("项目总金额（元）")
        form_layout.addRow("金额 *:", self.amount_edit)

        # 是否需要报告
        self.need_report_checkbox = QCheckBox("需要提交项目报告")
        self.need_report_checkbox.setToolTip("是否需要提交项目开发报告")
        form_layout.addRow("报告要求:", self.need_report_checkbox)

        # 联系方式
        self.contact_info_edit = QLineEdit()
        self.contact_info_edit.setPlaceholderText("邮箱、电话或其他联系方式")
        self.contact_info_edit.setToolTip("客户联系方式")
        form_layout.addRow("联系方式 *:", self.contact_info_edit)

        # 备注
        self.remarks_edit = QTextEdit()
        self.remarks_edit.setMaximumHeight(80)
        self.remarks_edit.setPlaceholderText("其他备注信息...")
        self.remarks_edit.setToolTip("其他需要记录的信息")
        form_layout.addRow("备注:", self.remarks_edit)

        layout.addLayout(form_layout)

        # 必填项提示
        required_label = QLabel("* 表示必填项")
        required_label.setStyleSheet("color: #dc3545; font-size: 12px;")
        layout.addWidget(required_label)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.button(QDialogButtonBox.Ok).setText("确定添加")
        button_box.button(QDialogButtonBox.Cancel).setText("取消")
        button_box.button(QDialogButtonBox.Ok).setStyleSheet(StyleManager.get_button_style("success"))
        button_box.accepted.connect(self.accept_order)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # 初始化入库编号
        self.on_language_changed()

    def on_language_changed(self):
        """编程语言改变时更新入库编号"""
        language = self.language_combo.currentText()
        next_number = self.db_manager.get_next_order_number(language)
        self.order_number_edit.setText(next_number)

    def accept_order(self):
        """确认添加记录"""
        if not self.validate_form():
            return

        order_data = {
            'order_number': self.order_number_edit.text().strip(),
            'delivery_date': self.delivery_date_edit.date().toString('yyyy-MM-dd'),
            'requirement_desc': self.requirement_desc_edit.toPlainText().strip(),
            'amount': float(self.amount_edit.text().strip()),
            'need_report': self.need_report_checkbox.isChecked(),
            'contact_info': self.contact_info_edit.text().strip(),
            'remarks': self.remarks_edit.toPlainText().strip(),
            'programming_language': self.language_combo.currentText()
        }

        if self.db_manager.add_order(order_data):
            self.accept()
        else:
            QMessageBox.warning(self, "错误", "添加记录失败，入库编号可能已存在！")

    def validate_form(self) -> bool:
        """验证表单数据"""
        if not self.order_number_edit.text().strip():
            QMessageBox.warning(self, "验证错误", "入库编号不能为空！")
            return False

        if not self.requirement_desc_edit.toPlainText().strip():
            QMessageBox.warning(self, "验证错误", "需求描述不能为空！")
            return False

        try:
            amount = float(self.amount_edit.text().strip())
            if amount <= 0:
                QMessageBox.warning(self, "验证错误", "金额必须大于0！")
                return False
        except ValueError:
            QMessageBox.warning(self, "验证错误", "请输入有效的金额！")
            return False

        if not self.contact_info_edit.text().strip():
            QMessageBox.warning(self, "验证错误", "联系方式不能为空！")
            return False

        return True


class EditOrderDialog(QDialog):
    """编辑接单记录对话框"""

    def __init__(self, order_data: Dict, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.order_data = order_data.copy()
        self.db_manager = db_manager
        self.setWindowTitle("✏️ 编辑接单记录")
        self.setModal(True)
        self.resize(500, 600)
        self.setStyleSheet(StyleManager.get_dialog_style())
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("✏️ 编辑接单记录")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #495057;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # 表单布局
        form_layout = QFormLayout()
        form_layout.setSpacing(12)

        # 通用输入框样式
        input_style = StyleManager.get_input_style()

        # 入库编号
        self.order_number_edit = QLineEdit()
        self.order_number_edit.setStyleSheet(input_style)
        form_layout.addRow("入库编号:", self.order_number_edit)

        # 编程语言
        self.language_combo = QComboBox()
        self.language_combo.addItems(PROGRAMMING_LANGUAGES)
        self.language_combo.setStyleSheet(input_style)
        form_layout.addRow("编程语言:", self.language_combo)

        # 交付日期
        self.delivery_date_edit = QDateEdit()
        self.delivery_date_edit.setDate(QDate.currentDate())
        self.delivery_date_edit.setCalendarPopup(True)
        self.delivery_date_edit.setStyleSheet(input_style)
        form_layout.addRow("交付日期:", self.delivery_date_edit)

        # 需求描述
        self.requirement_desc_edit = QTextEdit()
        self.requirement_desc_edit.setMaximumHeight(100)
        self.requirement_desc_edit.setStyleSheet(input_style)
        form_layout.addRow("需求描述:", self.requirement_desc_edit)

        # 金额
        self.amount_edit = QLineEdit()
        self.amount_edit.setPlaceholderText("请输入金额（数字）")
        self.amount_edit.setStyleSheet(input_style)
        form_layout.addRow("金额:", self.amount_edit)

        # 是否需要报告
        self.need_report_checkbox = QCheckBox("需要提交项目报告")
        self.need_report_checkbox.setStyleSheet(StyleManager.get_checkbox_style())
        form_layout.addRow("报告要求:", self.need_report_checkbox)

        # 联系方式
        self.contact_info_edit = QLineEdit()
        self.contact_info_edit.setPlaceholderText("邮箱、电话或其他联系方式")
        self.contact_info_edit.setStyleSheet(input_style)
        form_layout.addRow("联系方式:", self.contact_info_edit)

        # 备注
        self.remarks_edit = QTextEdit()
        self.remarks_edit.setMaximumHeight(80)
        self.remarks_edit.setPlaceholderText("可选的备注信息")
        self.remarks_edit.setStyleSheet(input_style)
        form_layout.addRow("备注:", self.remarks_edit)

        layout.addLayout(form_layout)

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet(StyleManager.get_button_style("secondary"))
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        save_btn = QPushButton("保存修改")
        save_btn.setStyleSheet(StyleManager.get_button_style("success"))
        save_btn.clicked.connect(self.save_changes)
        button_layout.addWidget(save_btn)

        layout.addLayout(button_layout)

    def load_data(self):
        """加载现有数据到表单"""
        self.order_number_edit.setText(self.order_data.get('order_number', ''))

        # 设置编程语言
        language = self.order_data.get('programming_language', '')
        index = self.language_combo.findText(language)
        if index >= 0:
            self.language_combo.setCurrentIndex(index)

        # 设置交付日期
        delivery_date = self.order_data.get('delivery_date', '')
        if delivery_date:
            date = QDate.fromString(delivery_date, 'yyyy-MM-dd')
            if date.isValid():
                self.delivery_date_edit.setDate(date)

        self.requirement_desc_edit.setPlainText(self.order_data.get('requirement_desc', ''))
        self.amount_edit.setText(str(self.order_data.get('amount', '')))
        self.need_report_checkbox.setChecked(self.order_data.get('need_report', False))
        self.contact_info_edit.setText(self.order_data.get('contact_info', ''))
        self.remarks_edit.setPlainText(self.order_data.get('remarks', ''))

    def save_changes(self):
        """保存修改"""
        if not self.validate_form():
            return

        # 收集表单数据
        updated_data = {
            'order_number': self.order_number_edit.text().strip(),
            'programming_language': self.language_combo.currentText(),
            'delivery_date': self.delivery_date_edit.date().toString('yyyy-MM-dd'),
            'requirement_desc': self.requirement_desc_edit.toPlainText().strip(),
            'amount': float(self.amount_edit.text().strip()),
            'need_report': self.need_report_checkbox.isChecked(),
            'contact_info': self.contact_info_edit.text().strip(),
            'remarks': self.remarks_edit.toPlainText().strip()
        }

        # 检查入库编号是否已存在（排除当前记录）
        if updated_data['order_number'] != self.order_data['order_number']:
            if self.db_manager.check_order_number_exists(updated_data['order_number']):
                QMessageBox.warning(self, "错误", "该入库编号已存在！")
                return

        # 更新数据库
        if self.db_manager.update_order(self.order_data['id'], updated_data):
            self.accept()
        else:
            QMessageBox.warning(self, "错误", "保存失败！")

    def validate_form(self) -> bool:
        """验证表单数据"""
        if not self.order_number_edit.text().strip():
            QMessageBox.warning(self, "验证错误", "入库编号不能为空！")
            return False

        if not self.requirement_desc_edit.toPlainText().strip():
            QMessageBox.warning(self, "验证错误", "需求描述不能为空！")
            return False

        try:
            amount = float(self.amount_edit.text().strip())
            if amount <= 0:
                QMessageBox.warning(self, "验证错误", "金额必须大于0！")
                return False
        except ValueError:
            QMessageBox.warning(self, "验证错误", "请输入有效的金额！")
            return False

        if not self.contact_info_edit.text().strip():
            QMessageBox.warning(self, "验证错误", "联系方式不能为空！")
            return False

        return True


class ImportPreviewDialog(QDialog):
    """导入预览对话框"""

    def __init__(self, orders: List[Dict], db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.orders = orders
        self.db_manager = db_manager
        self.setWindowTitle("📥 导入预览")
        self.setModal(True)
        self.resize(1000, 600)
        self.setStyleSheet(StyleManager.get_dialog_style())
        self.setup_ui()

    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel(f"📥 导入预览 - 共发现 {len(self.orders)} 条记录")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #495057;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # 说明文本
        info_label = QLabel("请检查以下数据，取消勾选不需要导入的记录：")
        info_label.setStyleSheet("color: #6c757d; margin-bottom: 10px;")
        layout.addWidget(info_label)

        # 创建预览表格
        self.preview_table = QTableWidget()
        self.setup_preview_table()
        layout.addWidget(self.preview_table)

        # 统计信息
        stats_layout = QHBoxLayout()

        # 全选/全不选按钮
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self.select_all)
        stats_layout.addWidget(select_all_btn)

        select_none_btn = QPushButton("全不选")
        select_none_btn.clicked.connect(self.select_none)
        stats_layout.addWidget(select_none_btn)

        stats_layout.addStretch()

        # 冲突检测
        self.conflict_label = QLabel()
        self.update_conflict_info()
        stats_layout.addWidget(self.conflict_label)

        layout.addLayout(stats_layout)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.button(QDialogButtonBox.Ok).setText("确定导入")
        button_box.button(QDialogButtonBox.Cancel).setText("取消")
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def setup_preview_table(self):
        """设置预览表格"""
        columns = ['选择', '入库编号', '编程语言', '交付日期', '需求描述', '金额', '是否需要报告', '联系方式', '备注', '状态']
        self.preview_table.setColumnCount(len(columns))
        self.preview_table.setHorizontalHeaderLabels(columns)
        self.preview_table.setRowCount(len(self.orders))

        # 设置表格属性
        self.preview_table.setAlternatingRowColors(True)
        self.preview_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        # 填充数据
        for row, order in enumerate(self.orders):
            # 选择复选框
            checkbox = QCheckBox()
            checkbox.setChecked(True)
            checkbox.stateChanged.connect(self.update_conflict_info)
            self.preview_table.setCellWidget(row, 0, checkbox)

            # 入库编号
            self.preview_table.setItem(row, 1, QTableWidgetItem(order.get('order_number', '')))

            # 编程语言
            self.preview_table.setItem(row, 2, QTableWidgetItem(order.get('programming_language', '')))

            # 交付日期
            self.preview_table.setItem(row, 3, QTableWidgetItem(order.get('delivery_date', '')))

            # 需求描述
            desc = order.get('requirement_desc', '')
            desc_item = QTableWidgetItem(desc[:50] + "..." if len(desc) > 50 else desc)
            desc_item.setToolTip(desc)
            self.preview_table.setItem(row, 4, desc_item)

            # 金额
            amount_item = QTableWidgetItem(f"¥{order.get('amount', 0):.2f}")
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.preview_table.setItem(row, 5, amount_item)

            # 是否需要报告
            report_item = QTableWidgetItem("是" if order.get('need_report', False) else "否")
            report_item.setTextAlignment(Qt.AlignCenter)
            self.preview_table.setItem(row, 6, report_item)

            # 联系方式
            self.preview_table.setItem(row, 7, QTableWidgetItem(order.get('contact_info', '')))

            # 备注
            remarks = order.get('remarks', '')
            remarks_item = QTableWidgetItem(remarks[:30] + "..." if len(remarks) > 30 else remarks)
            remarks_item.setToolTip(remarks)
            self.preview_table.setItem(row, 8, remarks_item)

            # 状态检查
            status = self.check_record_status(order)
            status_item = QTableWidgetItem(status)
            if "冲突" in status:
                status_item.setForeground(QColor("#dc3545"))
            elif "新记录" in status:
                status_item.setForeground(QColor("#28a745"))
            else:
                status_item.setForeground(QColor("#ffc107"))
            self.preview_table.setItem(row, 9, status_item)

        # 设置列宽
        header = self.preview_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 选择
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 入库编号
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 编程语言
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 交付日期
        header.setSectionResizeMode(4, QHeaderView.Stretch)           # 需求描述
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # 金额
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # 是否需要报告
        header.setSectionResizeMode(7, QHeaderView.Interactive)       # 联系方式
        header.setSectionResizeMode(8, QHeaderView.Interactive)       # 备注
        header.setSectionResizeMode(9, QHeaderView.ResizeToContents)  # 状态

    def check_record_status(self, order: Dict) -> str:
        """检查记录状态"""
        order_number = order.get('order_number', '')
        if not order_number:
            return "⚠️ 缺少编号"

        # 检查是否已存在
        if self.db_manager.check_order_number_exists(order_number):
            return "❌ 编号冲突"

        # 检查必要字段
        if not order.get('requirement_desc', ''):
            return "⚠️ 缺少描述"

        if not order.get('contact_info', ''):
            return "⚠️ 缺少联系方式"

        return "✅ 新记录"

    def select_all(self):
        """全选"""
        for row in range(self.preview_table.rowCount()):
            checkbox = self.preview_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)

    def select_none(self):
        """全不选"""
        for row in range(self.preview_table.rowCount()):
            checkbox = self.preview_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)

    def update_conflict_info(self):
        """更新冲突信息"""
        selected_count = 0
        conflict_count = 0

        for row in range(self.preview_table.rowCount()):
            checkbox = self.preview_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                selected_count += 1
                status_item = self.preview_table.item(row, 9)
                if status_item and "冲突" in status_item.text():
                    conflict_count += 1

        if conflict_count > 0:
            self.conflict_label.setText(f"⚠️ 已选择 {selected_count} 条记录，其中 {conflict_count} 条存在冲突")
            self.conflict_label.setStyleSheet("color: #dc3545; font-weight: bold;")
        else:
            self.conflict_label.setText(f"✅ 已选择 {selected_count} 条记录，无冲突")
            self.conflict_label.setStyleSheet("color: #28a745; font-weight: bold;")

    def get_selected_orders(self) -> List[Dict]:
        """获取选中的记录"""
        selected_orders = []
        for row in range(self.preview_table.rowCount()):
            checkbox = self.preview_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                selected_orders.append(self.orders[row])
        return selected_orders
