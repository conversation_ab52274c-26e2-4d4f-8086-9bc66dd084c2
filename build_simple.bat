@echo off
chcp 65001 >nul
echo ========================================
echo 项目接单管理工具 - 简单打包脚本
echo ========================================
echo.

echo 清理之前的构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"

echo.
echo 开始打包（单文件模式）...
pyinstaller --onefile --windowed --name="项目接单管理工具" ^
    --hidden-import=PyQt5.QtCore ^
    --hidden-import=PyQt5.QtGui ^
    --hidden-import=PyQt5.QtWidgets ^
    --hidden-import=matplotlib.backends.backend_qt5agg ^
    --hidden-import=matplotlib.figure ^
    --hidden-import=matplotlib.pyplot ^
    --collect-data=matplotlib ^
    main.py

echo.
if exist "dist\项目接单管理工具.exe" (
    echo ✓ 打包成功！
    echo 可执行文件: dist\项目接单管理工具.exe
) else (
    echo ✗ 打包失败！
)

pause
