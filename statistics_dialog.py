#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计对话框模块
包含统计分析对话框的定义
"""

from datetime import datetime
from typing import List, Dict
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QDialogButtonBox, QPushButton, QWidget, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor

from config import MATPLOTLIB_AVAILABLE
from styles import StyleManager
from database import DatabaseManager

if MATPLOTLIB_AVAILABLE:
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure


class StatisticsDialog(QDialog):
    """统计信息对话框"""

    def __init__(self, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setWindowTitle("📊 统计分析")
        self.setModal(True)
        self.resize(900, 700)
        self.setStyleSheet(StyleManager.get_dialog_style())
        self.setup_ui()
        self.load_statistics()

    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("📊 项目统计分析")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #495057;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title_label)

        # 创建选项卡
        self.tab_widget = QWidget()
        tab_layout = QHBoxLayout(self.tab_widget)

        # 左侧统计信息
        self.create_statistics_panel(tab_layout)

        # 右侧图表区域
        if MATPLOTLIB_AVAILABLE:
            self.create_chart_panel(tab_layout)
        else:
            self.create_no_chart_panel(tab_layout)

        layout.addWidget(self.tab_widget)

        # 关闭按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Close)
        button_box.button(QDialogButtonBox.Close).setText("关闭")
        button_box.rejected.connect(self.accept)
        layout.addWidget(button_box)

    def create_statistics_panel(self, parent_layout):
        """创建统计信息面板"""
        stats_widget = QWidget()
        stats_widget.setMaximumWidth(400)
        stats_layout = QVBoxLayout(stats_widget)

        # 总体统计
        overall_group, self.overall_layout = self.create_group_box("📈 总体统计")
        stats_layout.addWidget(overall_group)

        # 按编程语言统计
        language_group, self.language_layout = self.create_group_box("💻 按编程语言统计")
        stats_layout.addWidget(language_group)

        # 按报告要求统计
        report_group, self.report_layout = self.create_group_box("📋 按报告要求统计")
        stats_layout.addWidget(report_group)

        # 按月份统计
        monthly_group, self.monthly_layout = self.create_group_box("📅 按月份统计")
        stats_layout.addWidget(monthly_group)

        parent_layout.addWidget(stats_widget)

    def create_chart_panel(self, parent_layout):
        """创建图表面板"""
        chart_widget = QWidget()
        chart_layout = QVBoxLayout(chart_widget)

        # 图表选择按钮
        button_layout = QHBoxLayout()

        self.lang_chart_btn = QPushButton("编程语言分布")
        self.lang_chart_btn.clicked.connect(lambda: self.show_chart('language'))
        button_layout.addWidget(self.lang_chart_btn)

        self.amount_chart_btn = QPushButton("金额分布")
        self.amount_chart_btn.clicked.connect(lambda: self.show_chart('amount'))
        button_layout.addWidget(self.amount_chart_btn)

        self.monthly_chart_btn = QPushButton("月度趋势")
        self.monthly_chart_btn.clicked.connect(lambda: self.show_chart('monthly'))
        button_layout.addWidget(self.monthly_chart_btn)

        chart_layout.addLayout(button_layout)

        # 图表画布
        self.figure = Figure(figsize=(8, 6))
        self.canvas = FigureCanvas(self.figure)
        chart_layout.addWidget(self.canvas)

        parent_layout.addWidget(chart_widget)

    def create_no_chart_panel(self, parent_layout):
        """创建无图表面板（matplotlib未安装时）"""
        no_chart_widget = QWidget()
        no_chart_layout = QVBoxLayout(no_chart_widget)

        info_label = QLabel("📊 图表功能不可用")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #6c757d;
                padding: 50px;
                border: 2px dashed #dee2e6;
                border-radius: 8px;
            }
        """)

        install_label = QLabel("请安装matplotlib以启用图表功能：\npip install matplotlib")
        install_label.setAlignment(Qt.AlignCenter)
        install_label.setStyleSheet("color: #495057; margin-top: 20px;")

        no_chart_layout.addWidget(info_label)
        no_chart_layout.addWidget(install_label)
        no_chart_layout.addStretch()

        parent_layout.addWidget(no_chart_widget)

    def create_group_box(self, title):
        """创建分组框"""
        group_box = QWidget()
        group_box.setStyleSheet(StyleManager.get_group_box_style())

        # 添加标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #495057;
                font-size: 14px;
                margin-bottom: 10px;
                background-color: transparent;
                border: none;
            }
        """)

        main_layout = QVBoxLayout(group_box)
        main_layout.addWidget(title_label)

        # 创建内容布局
        content_layout = QVBoxLayout()
        main_layout.addLayout(content_layout)

        return group_box, content_layout

    def load_statistics(self):
        """加载统计数据"""
        orders = self.db_manager.get_all_orders()

        if not orders:
            self.show_no_data_message()
            return

        # 总体统计
        self.load_overall_statistics(orders)

        # 按编程语言统计
        self.load_language_statistics(orders)

        # 按报告要求统计
        self.load_report_statistics(orders)

        # 按月份统计
        self.load_monthly_statistics(orders)

    def show_no_data_message(self):
        """显示无数据消息"""
        no_data_label = QLabel("📊 暂无数据")
        no_data_label.setAlignment(Qt.AlignCenter)
        no_data_label.setStyleSheet("color: #6c757d; font-size: 16px; padding: 20px;")
        self.overall_layout.addWidget(no_data_label)

    def load_overall_statistics(self, orders):
        """加载总体统计"""
        total_count = len(orders)
        total_amount = sum(order['amount'] for order in orders)
        avg_amount = total_amount / total_count if total_count > 0 else 0

        # 最高和最低金额
        amounts = [order['amount'] for order in orders]
        max_amount = max(amounts) if amounts else 0
        min_amount = min(amounts) if amounts else 0

        stats_text = f"""📊 项目总数：{total_count} 个
💰 总金额：¥{total_amount:.2f}
📈 平均金额：¥{avg_amount:.2f}
🔝 最高金额：¥{max_amount:.2f}
🔻 最低金额：¥{min_amount:.2f}"""

        stats_label = QLabel(stats_text)
        stats_label.setStyleSheet("color: #495057; line-height: 1.6;")
        self.overall_layout.addWidget(stats_label)

    def load_language_statistics(self, orders):
        """加载编程语言统计"""
        language_stats = {}
        for order in orders:
            lang = order['programming_language']
            if lang not in language_stats:
                language_stats[lang] = {'count': 0, 'amount': 0}
            language_stats[lang]['count'] += 1
            language_stats[lang]['amount'] += order['amount']

        # 按项目数量排序
        sorted_langs = sorted(language_stats.items(), key=lambda x: x[1]['count'], reverse=True)

        for lang, stats in sorted_langs:
            percentage = (stats['count'] / len(orders)) * 100
            lang_text = f"💻 {lang}: {stats['count']}个 (¥{stats['amount']:.2f}) - {percentage:.1f}%"
            lang_label = QLabel(lang_text)
            lang_label.setStyleSheet("color: #495057; margin: 2px 0;")
            self.language_layout.addWidget(lang_label)

    def load_report_statistics(self, orders):
        """加载报告要求统计"""
        need_report = sum(1 for order in orders if order['need_report'])
        no_report = len(orders) - need_report

        need_report_amount = sum(order['amount'] for order in orders if order['need_report'])
        no_report_amount = sum(order['amount'] for order in orders if not order['need_report'])

        report_text = f"""📋 需要报告：{need_report} 个 (¥{need_report_amount:.2f})
📄 无需报告：{no_report} 个 (¥{no_report_amount:.2f})"""

        report_label = QLabel(report_text)
        report_label.setStyleSheet("color: #495057; line-height: 1.6;")
        self.report_layout.addWidget(report_label)

    def load_monthly_statistics(self, orders):
        """加载月份统计"""
        monthly_stats = {}
        for order in orders:
            try:
                date_obj = datetime.strptime(order['delivery_date'], '%Y-%m-%d')
                month_key = date_obj.strftime('%Y-%m')
                if month_key not in monthly_stats:
                    monthly_stats[month_key] = {'count': 0, 'amount': 0}
                monthly_stats[month_key]['count'] += 1
                monthly_stats[month_key]['amount'] += order['amount']
            except ValueError:
                continue

        # 按月份排序
        sorted_months = sorted(monthly_stats.items())

        for month, stats in sorted_months[-6:]:  # 显示最近6个月
            month_text = f"📅 {month}: {stats['count']}个 (¥{stats['amount']:.2f})"
            month_label = QLabel(month_text)
            month_label.setStyleSheet("color: #495057; margin: 2px 0;")
            self.monthly_layout.addWidget(month_label)

    def show_chart(self, chart_type):
        """显示图表"""
        if not MATPLOTLIB_AVAILABLE:
            QMessageBox.information(self, "提示", "图表功能需要安装matplotlib库\n请运行: pip install matplotlib")
            return

        self.figure.clear()
        orders = self.db_manager.get_all_orders()

        if chart_type == 'language':
            self.create_language_chart(orders)
        elif chart_type == 'amount':
            self.create_amount_chart(orders)
        elif chart_type == 'monthly':
            self.create_monthly_chart(orders)

        self.canvas.draw()

    def create_language_chart(self, orders):
        """创建编程语言分布图"""
        if not MATPLOTLIB_AVAILABLE:
            return

        language_stats = {}
        for order in orders:
            lang = order['programming_language']
            language_stats[lang] = language_stats.get(lang, 0) + 1

        if not language_stats:
            return

        ax = self.figure.add_subplot(111)
        languages = list(language_stats.keys())
        counts = list(language_stats.values())

        colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#a29bfe', '#fd79a8']

        # 创建饼图，设置字体
        wedges, texts, autotexts = ax.pie(counts, labels=languages, autopct='%1.1f%%',
                                         colors=colors[:len(languages)], startangle=90)

        # 设置标签字体
        for text in texts:
            text.set_fontsize(10)
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontsize(9)
            autotext.set_weight('bold')

        ax.set_title('编程语言项目分布', fontsize=16, pad=20, weight='bold')

        # 确保图表是圆形
        ax.axis('equal')

    def create_amount_chart(self, orders):
        """创建金额分布图"""
        if not MATPLOTLIB_AVAILABLE:
            return

        language_amounts = {}
        for order in orders:
            lang = order['programming_language']
            language_amounts[lang] = language_amounts.get(lang, 0) + order['amount']

        if not language_amounts:
            return

        ax = self.figure.add_subplot(111)
        languages = list(language_amounts.keys())
        amounts = list(language_amounts.values())

        # 创建渐变色柱状图
        colors = ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1', '#fd7e14', '#20c997']
        bars = ax.bar(languages, amounts, color=colors[:len(languages)], alpha=0.8, edgecolor='white', linewidth=1)

        # 设置标题和标签
        ax.set_title('各编程语言总金额', fontsize=16, pad=20, weight='bold')
        ax.set_ylabel('金额 (元)', fontsize=12, weight='bold')
        ax.set_xlabel('编程语言', fontsize=12, weight='bold')

        # 在柱子上显示数值
        for bar, amount in zip(bars, amounts):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'¥{amount:.0f}', ha='center', va='bottom', fontsize=10, weight='bold')

        # 旋转x轴标签以避免重叠
        ax.tick_params(axis='x', rotation=45)

        # 调整布局
        self.figure.tight_layout()

    def create_monthly_chart(self, orders):
        """创建月度趋势图"""
        if not MATPLOTLIB_AVAILABLE:
            return

        monthly_stats = {}
        for order in orders:
            try:
                date_obj = datetime.strptime(order['delivery_date'], '%Y-%m-%d')
                month_key = date_obj.strftime('%Y-%m')
                if month_key not in monthly_stats:
                    monthly_stats[month_key] = {'count': 0, 'amount': 0}
                monthly_stats[month_key]['count'] += 1
                monthly_stats[month_key]['amount'] += order['amount']
            except ValueError:
                continue

        if not monthly_stats:
            return

        # 按月份排序
        sorted_months = sorted(monthly_stats.items())
        months = [item[0] for item in sorted_months]
        counts = [item[1]['count'] for item in sorted_months]
        amounts = [item[1]['amount'] for item in sorted_months]

        ax = self.figure.add_subplot(111)

        # 创建双y轴图表
        ax2 = ax.twinx()

        # 绘制项目数量（柱状图）
        bars = ax.bar(months, counts, alpha=0.7, color='#007bff', label='项目数量')

        # 绘制金额（折线图）
        line = ax2.plot(months, amounts, color='#dc3545', marker='o', linewidth=2, markersize=6, label='总金额')

        # 设置标题和标签
        ax.set_title('月度项目趋势', fontsize=16, pad=20, weight='bold')
        ax.set_xlabel('月份', fontsize=12, weight='bold')
        ax.set_ylabel('项目数量', fontsize=12, weight='bold', color='#007bff')
        ax2.set_ylabel('金额 (元)', fontsize=12, weight='bold', color='#dc3545')

        # 设置y轴颜色
        ax.tick_params(axis='y', labelcolor='#007bff')
        ax2.tick_params(axis='y', labelcolor='#dc3545')

        # 旋转x轴标签
        ax.tick_params(axis='x', rotation=45)

        # 添加图例
        lines1, labels1 = ax.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

        # 调整布局
        self.figure.tight_layout()
