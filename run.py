#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动脚本 - 处理PyQt5环境问题
"""

import os
import sys

def setup_qt_environment():
    """设置Qt环境变量"""
    try:
        import PyQt5
        qt_plugin_path = os.path.join(os.path.dirname(PyQt5.__file__), 'Qt5', 'plugins')
        if os.path.exists(qt_plugin_path):
            os.environ['QT_PLUGIN_PATH'] = qt_plugin_path
            print(f"设置QT_PLUGIN_PATH: {qt_plugin_path}")
        
        # 设置Qt平台插件
        if sys.platform == 'win32':
            os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = qt_plugin_path
            
    except ImportError:
        print("PyQt5未安装，请先运行: pip install PyQt5")
        return False
    
    return True

def main():
    """主函数"""
    print("=== 项目接单管理工具启动器 ===")

    if not setup_qt_environment():
        return

    try:
        # 设置高DPI支持（必须在导入main之前）
        import sys
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt

        # 设置高DPI属性
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

        # 导入并运行主程序
        from main import main as main_app
        main_app()
    except Exception as e:
        print(f"启动失败: {e}")
        print("\n尝试基本测试:")
        print("python test_basic.py")

if __name__ == '__main__':
    main()
