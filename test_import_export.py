#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入导出功能
"""

from database import DatabaseManager
from utils import generate_markdown_content, parse_markdown_content
from datetime import datetime


def test_import_export():
    """测试导入导出功能"""
    print("开始测试导入导出功能...")
    
    # 创建测试数据库
    db_manager = DatabaseManager("test_orders.db")
    
    # 添加一些测试数据
    test_orders = [
        {
            'order_number': 'P1001',
            'delivery_date': '2024-01-15',
            'requirement_desc': '开发一个简单的计算器程序，支持基本的四则运算功能',
            'amount': 1500.00,
            'need_report': True,
            'contact_info': '<EMAIL>',
            'remarks': '需要在一周内完成',
            'programming_language': 'Python'
        },
        {
            'order_number': 'J1001',
            'delivery_date': '2024-01-20',
            'requirement_desc': '开发一个学生管理系统，包含增删改查功能',
            'amount': 2500.50,
            'need_report': False,
            'contact_info': '13800138000',
            'remarks': '',
            'programming_language': 'Java'
        },
        {
            'order_number': 'C2001',
            'delivery_date': '2024-01-25',
            'requirement_desc': '实现一个简单的文件管理器，支持文件的复制、移动、删除等操作',
            'amount': 3000.00,
            'need_report': True,
            'contact_info': '<EMAIL>',
            'remarks': '需要详细的技术文档',
            'programming_language': 'C++'
        }
    ]
    
    # 添加测试数据到数据库
    for order in test_orders:
        success = db_manager.add_order(order)
        print(f"添加订单 {order['order_number']}: {'成功' if success else '失败'}")
    
    # 获取所有订单
    all_orders = db_manager.get_all_orders()
    print(f"数据库中共有 {len(all_orders)} 条记录")
    
    # 按编程语言分组
    language_groups = {}
    for order in all_orders:
        lang = order['programming_language']
        if lang not in language_groups:
            language_groups[lang] = []
        language_groups[lang].append(order)
    
    # 生成Markdown内容
    markdown_content = generate_markdown_content(language_groups)
    
    # 保存到文件
    with open('test_export.md', 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    print("导出测试文件: test_export.md")
    
    # 读取并解析Markdown文件
    with open('test_export.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 解析Markdown内容
    parsed_orders = parse_markdown_content(content)
    print(f"从Markdown文件解析出 {len(parsed_orders)} 条记录")
    
    # 验证解析结果
    print("\n解析结果验证:")
    for i, order in enumerate(parsed_orders):
        print(f"记录 {i+1}:")
        print(f"  入库编号: {order.get('order_number', 'N/A')}")
        print(f"  编程语言: {order.get('programming_language', 'N/A')}")
        print(f"  交付日期: {order.get('delivery_date', 'N/A')}")
        print(f"  金额: {order.get('amount', 'N/A')}")
        print(f"  需要报告: {order.get('need_report', 'N/A')}")
        print(f"  联系方式: {order.get('contact_info', 'N/A')}")
        print(f"  需求描述: {order.get('requirement_desc', 'N/A')[:50]}...")
        print(f"  备注: {order.get('remarks', 'N/A')}")
        print()
    
    # 检查数据完整性
    print("数据完整性检查:")
    for i, (original, parsed) in enumerate(zip(test_orders, parsed_orders)):
        print(f"记录 {i+1} 比较:")
        
        # 检查关键字段
        fields_to_check = ['order_number', 'programming_language', 'delivery_date', 'amount', 'need_report', 'contact_info']
        
        for field in fields_to_check:
            original_value = original.get(field)
            parsed_value = parsed.get(field)
            
            if field == 'amount':
                # 金额比较允许小的浮点误差
                if abs(float(original_value) - float(parsed_value)) < 0.01:
                    status = "✓"
                else:
                    status = "✗"
            else:
                status = "✓" if original_value == parsed_value else "✗"
            
            print(f"  {field}: {status} (原始: {original_value}, 解析: {parsed_value})")
        print()
    
    print("测试完成!")


if __name__ == '__main__':
    test_import_export()
