(['C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\main.py'],
 ['C:\\Users\\<USER>\\Desktop\\tools\\projectManage'],
 ['PyQt5.QtCore',
  'PyQt5.QtGui',
  'PyQt5.QtWidgets',
  'matplotlib.backends.backend_qt5agg',
  'matplotlib.figure',
  'matplotlib.pyplot'],
 [('C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('matplotlib\\__init__.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\__init__.pyi',
   'DATA'),
  ('matplotlib\\_api\\__init__.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_api\\__init__.pyi',
   'DATA'),
  ('matplotlib\\_api\\deprecation.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_api\\deprecation.pyi',
   'DATA'),
  ('matplotlib\\_c_internal_utils.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_c_internal_utils.pyi',
   'DATA'),
  ('matplotlib\\_color_data.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_color_data.pyi',
   'DATA'),
  ('matplotlib\\_docstring.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_docstring.pyi',
   'DATA'),
  ('matplotlib\\_enums.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_enums.pyi',
   'DATA'),
  ('matplotlib\\_image.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_image.pyi',
   'DATA'),
  ('matplotlib\\_path.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_path.pyi',
   'DATA'),
  ('matplotlib\\_pylab_helpers.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_pylab_helpers.pyi',
   'DATA'),
  ('matplotlib\\_qhull.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_qhull.pyi',
   'DATA'),
  ('matplotlib\\_tri.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_tri.pyi',
   'DATA'),
  ('matplotlib\\animation.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\animation.pyi',
   'DATA'),
  ('matplotlib\\artist.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\artist.pyi',
   'DATA'),
  ('matplotlib\\axes\\__init__.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\axes\\__init__.pyi',
   'DATA'),
  ('matplotlib\\axes\\_axes.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_axes.pyi',
   'DATA'),
  ('matplotlib\\axes\\_base.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_base.pyi',
   'DATA'),
  ('matplotlib\\axes\\_secondary_axes.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_secondary_axes.pyi',
   'DATA'),
  ('matplotlib\\axis.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\axis.pyi',
   'DATA'),
  ('matplotlib\\backend_bases.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backend_bases.pyi',
   'DATA'),
  ('matplotlib\\backend_managers.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backend_managers.pyi',
   'DATA'),
  ('matplotlib\\backend_tools.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backend_tools.pyi',
   'DATA'),
  ('matplotlib\\backends\\_backend_agg.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\_backend_agg.pyi',
   'DATA'),
  ('matplotlib\\backends\\_macosx.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\_macosx.pyi',
   'DATA'),
  ('matplotlib\\backends\\_tkagg.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\_tkagg.pyi',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\all_figures.html',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\all_figures.html',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\css\\boilerplate.css',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\css\\boilerplate.css',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\css\\fbm.css',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\css\\fbm.css',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\css\\mpl.css',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\css\\mpl.css',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\css\\page.css',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\css\\page.css',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\ipython_inline_figure.html',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\ipython_inline_figure.html',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\js\\mpl.js',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\js\\mpl.js',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\js\\mpl_tornado.js',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\js\\mpl_tornado.js',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\js\\nbagg_mpl.js',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\js\\nbagg_mpl.js',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\single_figure.html',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\single_figure.html',
   'DATA'),
  ('matplotlib\\bezier.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\bezier.pyi',
   'DATA'),
  ('matplotlib\\cbook.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\cbook.pyi',
   'DATA'),
  ('matplotlib\\cm.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\cm.pyi',
   'DATA'),
  ('matplotlib\\collections.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\collections.pyi',
   'DATA'),
  ('matplotlib\\colorbar.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\colorbar.pyi',
   'DATA'),
  ('matplotlib\\colorizer.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\colorizer.pyi',
   'DATA'),
  ('matplotlib\\colors.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\colors.pyi',
   'DATA'),
  ('matplotlib\\container.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\container.pyi',
   'DATA'),
  ('matplotlib\\contour.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\contour.pyi',
   'DATA'),
  ('matplotlib\\dviread.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\dviread.pyi',
   'DATA'),
  ('matplotlib\\figure.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\figure.pyi',
   'DATA'),
  ('matplotlib\\font_manager.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\font_manager.pyi',
   'DATA'),
  ('matplotlib\\ft2font.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\ft2font.pyi',
   'DATA'),
  ('matplotlib\\gridspec.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\gridspec.pyi',
   'DATA'),
  ('matplotlib\\hatch.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\hatch.pyi',
   'DATA'),
  ('matplotlib\\image.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\image.pyi',
   'DATA'),
  ('matplotlib\\inset.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\inset.pyi',
   'DATA'),
  ('matplotlib\\layout_engine.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\layout_engine.pyi',
   'DATA'),
  ('matplotlib\\legend.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\legend.pyi',
   'DATA'),
  ('matplotlib\\legend_handler.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\legend_handler.pyi',
   'DATA'),
  ('matplotlib\\lines.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\lines.pyi',
   'DATA'),
  ('matplotlib\\markers.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\markers.pyi',
   'DATA'),
  ('matplotlib\\mathtext.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mathtext.pyi',
   'DATA'),
  ('matplotlib\\mlab.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mlab.pyi',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\kpsewhich.lua',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\kpsewhich.lua',
   'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA'),
  ('matplotlib\\offsetbox.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\offsetbox.pyi',
   'DATA'),
  ('matplotlib\\patches.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\patches.pyi',
   'DATA'),
  ('matplotlib\\path.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\path.pyi',
   'DATA'),
  ('matplotlib\\patheffects.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\patheffects.pyi',
   'DATA'),
  ('matplotlib\\projections\\__init__.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\projections\\__init__.pyi',
   'DATA'),
  ('matplotlib\\projections\\geo.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\projections\\geo.pyi',
   'DATA'),
  ('matplotlib\\projections\\polar.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\projections\\polar.pyi',
   'DATA'),
  ('matplotlib\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\py.typed',
   'DATA'),
  ('matplotlib\\quiver.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\quiver.pyi',
   'DATA'),
  ('matplotlib\\rcsetup.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\rcsetup.pyi',
   'DATA'),
  ('matplotlib\\sankey.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\sankey.pyi',
   'DATA'),
  ('matplotlib\\scale.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\scale.pyi',
   'DATA'),
  ('matplotlib\\spines.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\spines.pyi',
   'DATA'),
  ('matplotlib\\stackplot.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\stackplot.pyi',
   'DATA'),
  ('matplotlib\\streamplot.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\streamplot.pyi',
   'DATA'),
  ('matplotlib\\style\\core.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\style\\core.pyi',
   'DATA'),
  ('matplotlib\\table.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\table.pyi',
   'DATA'),
  ('matplotlib\\testing\\__init__.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\testing\\__init__.pyi',
   'DATA'),
  ('matplotlib\\testing\\compare.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\testing\\compare.pyi',
   'DATA'),
  ('matplotlib\\testing\\conftest.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\testing\\conftest.pyi',
   'DATA'),
  ('matplotlib\\testing\\decorators.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\testing\\decorators.pyi',
   'DATA'),
  ('matplotlib\\testing\\widgets.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\testing\\widgets.pyi',
   'DATA'),
  ('matplotlib\\texmanager.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\texmanager.pyi',
   'DATA'),
  ('matplotlib\\text.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\text.pyi',
   'DATA'),
  ('matplotlib\\textpath.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\textpath.pyi',
   'DATA'),
  ('matplotlib\\ticker.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\ticker.pyi',
   'DATA'),
  ('matplotlib\\transforms.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\transforms.pyi',
   'DATA'),
  ('matplotlib\\tri\\_triangulation.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triangulation.pyi',
   'DATA'),
  ('matplotlib\\tri\\_tricontour.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tricontour.pyi',
   'DATA'),
  ('matplotlib\\tri\\_trifinder.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_trifinder.pyi',
   'DATA'),
  ('matplotlib\\tri\\_triinterpolate.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triinterpolate.pyi',
   'DATA'),
  ('matplotlib\\tri\\_tripcolor.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tripcolor.pyi',
   'DATA'),
  ('matplotlib\\tri\\_triplot.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triplot.pyi',
   'DATA'),
  ('matplotlib\\tri\\_trirefine.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_trirefine.pyi',
   'DATA'),
  ('matplotlib\\tri\\_tritools.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tritools.pyi',
   'DATA'),
  ('matplotlib\\widgets.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\widgets.pyi',
   'DATA')],
 '3.13.4 (tags/v3.13.4:8a526ec, Jun  3 2025, 17:46:04) [MSC v.1943 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('main',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\main.py',
   'PYSOURCE')],
 [('_pyi_rth_utils.qt',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\csv.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\string.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\copy.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\random.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\argparse.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compression.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\struct.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bz2.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gettext.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bisect.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_strptime.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\calendar.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getopt.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\selectors.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\quopri.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tempfile.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextlib.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\textwrap.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\glob.py',
   'PYMODULE'),
  ('pathlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\inspect.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\token.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\opcode.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ast.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\__future__.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tokenize.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\subprocess.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\signal.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\difflib.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipimport.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ssl.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\queue.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\runpy.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\secrets.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hmac.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\netrc.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\client.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sysconfig\\__init__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('site',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site.py',
   'PYMODULE'),
  ('_pyrepl.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\main.py',
   'PYMODULE'),
  ('_pyrepl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\curses.py',
   'PYMODULE'),
  ('curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl.input',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\keymap.py',
   'PYMODULE'),
  ('_pyrepl.types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\types.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\commands.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\pager.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tty.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\reader.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\utils.py',
   'PYMODULE'),
  ('_colorize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_colorize.py',
   'PYMODULE'),
  ('_pyrepl.console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\console.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\codeop.py',
   'PYMODULE'),
  ('_pyrepl.trace',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\trace.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.base_eventqueue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\base_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_pyrepl.windows_eventqueue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\windows_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\readline.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('_ios_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_ios_support.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shlex.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socketserver.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\configparser.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\platform.py',
   'PYMODULE'),
  ('matplotlib',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('cycler',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\cycler\\__init__.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('pyparsing',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.common',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.core',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.util',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.path',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('kiwisolver',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\doctest.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pdb.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\cmd.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('matplotlib.text',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\colorsys.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.registry',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\registry.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fileinput.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\plistlib.py',
   'PYMODULE'),
  ('matplotlib.style',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('contourpy',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('contourpy.dechunk',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE'),
  ('contourpy.typecheck',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE'),
  ('contourpy.types',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\contourpy\\types.py',
   'PYMODULE'),
  ('contourpy.array',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\contourpy\\array.py',
   'PYMODULE'),
  ('contourpy.convert',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\contourpy\\convert.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy._version',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.table',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('six',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('dateutil',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('matplotlib.units',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.container',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.image',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.inset',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\inset.py',
   'PYMODULE'),
  ('matplotlib.category',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.colorizer',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\colorizer.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib._cm_bivar',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_cm_bivar.py',
   'PYMODULE'),
  ('matplotlib._cm_multivar',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_cm_multivar.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\uuid.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE'),
  ('matplotlib._version',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib._api',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qt5agg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_qt5agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qtagg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_qtagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qt',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_qt.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor.figureoptions',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\figureoptions.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor._formlayout',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\_formlayout.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_compat',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\qt_compat.py',
   'PYMODULE'),
  ('PyQt5',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.typing',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\typing.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\typing.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE')],
 [('python313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-860d95b1c38e637ce4509f5fa24fbf2a.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-860d95b1c38e637ce4509f5fa24fbf2a.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-a4c2229bdc2a2a630acdc095b4d86008.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy.libs\\msvcp140-a4c2229bdc2a2a630acdc095b4d86008.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('kiwisolver\\_cext.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\kiwisolver\\_cext.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\_webp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\_imagingtk.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\_avif.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\_imagingcms.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\_imagingmath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PIL\\_imaging.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_backend_agg.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\_backend_agg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('contourpy\\_contourpy.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\contourpy\\_contourpy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_tri.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_tri.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_qhull.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_qhull.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_image.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_image.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\ft2font.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\ft2font.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('matplotlib\\_path.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_path.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_c_internal_utils.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_c_internal_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\sip.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python3.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'D:\\Windows Kits\\10\\Windows Performance Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('matplotlib\\__init__.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\__init__.pyi',
   'DATA'),
  ('matplotlib\\_api\\__init__.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_api\\__init__.pyi',
   'DATA'),
  ('matplotlib\\_api\\deprecation.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_api\\deprecation.pyi',
   'DATA'),
  ('matplotlib\\_c_internal_utils.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_c_internal_utils.pyi',
   'DATA'),
  ('matplotlib\\_color_data.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_color_data.pyi',
   'DATA'),
  ('matplotlib\\_docstring.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_docstring.pyi',
   'DATA'),
  ('matplotlib\\_enums.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_enums.pyi',
   'DATA'),
  ('matplotlib\\_image.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_image.pyi',
   'DATA'),
  ('matplotlib\\_path.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_path.pyi',
   'DATA'),
  ('matplotlib\\_pylab_helpers.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_pylab_helpers.pyi',
   'DATA'),
  ('matplotlib\\_qhull.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_qhull.pyi',
   'DATA'),
  ('matplotlib\\_tri.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\_tri.pyi',
   'DATA'),
  ('matplotlib\\animation.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\animation.pyi',
   'DATA'),
  ('matplotlib\\artist.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\artist.pyi',
   'DATA'),
  ('matplotlib\\axes\\__init__.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\axes\\__init__.pyi',
   'DATA'),
  ('matplotlib\\axes\\_axes.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_axes.pyi',
   'DATA'),
  ('matplotlib\\axes\\_base.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_base.pyi',
   'DATA'),
  ('matplotlib\\axes\\_secondary_axes.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_secondary_axes.pyi',
   'DATA'),
  ('matplotlib\\axis.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\axis.pyi',
   'DATA'),
  ('matplotlib\\backend_bases.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backend_bases.pyi',
   'DATA'),
  ('matplotlib\\backend_managers.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backend_managers.pyi',
   'DATA'),
  ('matplotlib\\backend_tools.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backend_tools.pyi',
   'DATA'),
  ('matplotlib\\backends\\_backend_agg.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\_backend_agg.pyi',
   'DATA'),
  ('matplotlib\\backends\\_macosx.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\_macosx.pyi',
   'DATA'),
  ('matplotlib\\backends\\_tkagg.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\_tkagg.pyi',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\all_figures.html',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\all_figures.html',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\css\\boilerplate.css',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\css\\boilerplate.css',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\css\\fbm.css',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\css\\fbm.css',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\css\\mpl.css',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\css\\mpl.css',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\css\\page.css',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\css\\page.css',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\ipython_inline_figure.html',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\ipython_inline_figure.html',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\js\\mpl.js',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\js\\mpl.js',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\js\\mpl_tornado.js',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\js\\mpl_tornado.js',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\js\\nbagg_mpl.js',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\js\\nbagg_mpl.js',
   'DATA'),
  ('matplotlib\\backends\\web_backend\\single_figure.html',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\single_figure.html',
   'DATA'),
  ('matplotlib\\bezier.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\bezier.pyi',
   'DATA'),
  ('matplotlib\\cbook.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\cbook.pyi',
   'DATA'),
  ('matplotlib\\cm.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\cm.pyi',
   'DATA'),
  ('matplotlib\\collections.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\collections.pyi',
   'DATA'),
  ('matplotlib\\colorbar.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\colorbar.pyi',
   'DATA'),
  ('matplotlib\\colorizer.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\colorizer.pyi',
   'DATA'),
  ('matplotlib\\colors.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\colors.pyi',
   'DATA'),
  ('matplotlib\\container.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\container.pyi',
   'DATA'),
  ('matplotlib\\contour.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\contour.pyi',
   'DATA'),
  ('matplotlib\\dviread.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\dviread.pyi',
   'DATA'),
  ('matplotlib\\figure.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\figure.pyi',
   'DATA'),
  ('matplotlib\\font_manager.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\font_manager.pyi',
   'DATA'),
  ('matplotlib\\ft2font.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\ft2font.pyi',
   'DATA'),
  ('matplotlib\\gridspec.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\gridspec.pyi',
   'DATA'),
  ('matplotlib\\hatch.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\hatch.pyi',
   'DATA'),
  ('matplotlib\\image.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\image.pyi',
   'DATA'),
  ('matplotlib\\inset.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\inset.pyi',
   'DATA'),
  ('matplotlib\\layout_engine.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\layout_engine.pyi',
   'DATA'),
  ('matplotlib\\legend.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\legend.pyi',
   'DATA'),
  ('matplotlib\\legend_handler.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\legend_handler.pyi',
   'DATA'),
  ('matplotlib\\lines.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\lines.pyi',
   'DATA'),
  ('matplotlib\\markers.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\markers.pyi',
   'DATA'),
  ('matplotlib\\mathtext.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mathtext.pyi',
   'DATA'),
  ('matplotlib\\mlab.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mlab.pyi',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\kpsewhich.lua',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\kpsewhich.lua',
   'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA'),
  ('matplotlib\\offsetbox.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\offsetbox.pyi',
   'DATA'),
  ('matplotlib\\patches.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\patches.pyi',
   'DATA'),
  ('matplotlib\\path.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\path.pyi',
   'DATA'),
  ('matplotlib\\patheffects.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\patheffects.pyi',
   'DATA'),
  ('matplotlib\\projections\\__init__.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\projections\\__init__.pyi',
   'DATA'),
  ('matplotlib\\projections\\geo.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\projections\\geo.pyi',
   'DATA'),
  ('matplotlib\\projections\\polar.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\projections\\polar.pyi',
   'DATA'),
  ('matplotlib\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\py.typed',
   'DATA'),
  ('matplotlib\\quiver.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\quiver.pyi',
   'DATA'),
  ('matplotlib\\rcsetup.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\rcsetup.pyi',
   'DATA'),
  ('matplotlib\\sankey.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\sankey.pyi',
   'DATA'),
  ('matplotlib\\scale.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\scale.pyi',
   'DATA'),
  ('matplotlib\\spines.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\spines.pyi',
   'DATA'),
  ('matplotlib\\stackplot.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\stackplot.pyi',
   'DATA'),
  ('matplotlib\\streamplot.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\streamplot.pyi',
   'DATA'),
  ('matplotlib\\style\\core.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\style\\core.pyi',
   'DATA'),
  ('matplotlib\\table.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\table.pyi',
   'DATA'),
  ('matplotlib\\testing\\__init__.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\testing\\__init__.pyi',
   'DATA'),
  ('matplotlib\\testing\\compare.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\testing\\compare.pyi',
   'DATA'),
  ('matplotlib\\testing\\conftest.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\testing\\conftest.pyi',
   'DATA'),
  ('matplotlib\\testing\\decorators.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\testing\\decorators.pyi',
   'DATA'),
  ('matplotlib\\testing\\widgets.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\testing\\widgets.pyi',
   'DATA'),
  ('matplotlib\\texmanager.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\texmanager.pyi',
   'DATA'),
  ('matplotlib\\text.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\text.pyi',
   'DATA'),
  ('matplotlib\\textpath.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\textpath.pyi',
   'DATA'),
  ('matplotlib\\ticker.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\ticker.pyi',
   'DATA'),
  ('matplotlib\\transforms.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\transforms.pyi',
   'DATA'),
  ('matplotlib\\tri\\_triangulation.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triangulation.pyi',
   'DATA'),
  ('matplotlib\\tri\\_tricontour.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tricontour.pyi',
   'DATA'),
  ('matplotlib\\tri\\_trifinder.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_trifinder.pyi',
   'DATA'),
  ('matplotlib\\tri\\_triinterpolate.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triinterpolate.pyi',
   'DATA'),
  ('matplotlib\\tri\\_tripcolor.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tripcolor.pyi',
   'DATA'),
  ('matplotlib\\tri\\_triplot.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triplot.pyi',
   'DATA'),
  ('matplotlib\\tri\\_trirefine.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_trirefine.pyi',
   'DATA'),
  ('matplotlib\\tri\\_tritools.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tritools.pyi',
   'DATA'),
  ('matplotlib\\widgets.pyi',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\matplotlib\\widgets.pyi',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('numpy-2.3.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy-2.3.2.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.3.2.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy-2.3.2.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.3.2.dist-info\\DELVEWHEEL',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy-2.3.2.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.3.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy-2.3.2.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.3.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy-2.3.2.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.3.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy-2.3.2.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.3.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\.venv\\Lib\\site-packages\\numpy-2.3.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\tools\\projectManage\\build\\项目接单管理工具\\base_library.zip',
   'DATA')],
 [('collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('warnings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\warnings.py',
   'PYMODULE'),
  ('ntpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ntpath.py',
   'PYMODULE'),
  ('keyword',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\keyword.py',
   'PYMODULE'),
  ('traceback',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\traceback.py',
   'PYMODULE'),
  ('functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\functools.py',
   'PYMODULE'),
  ('io',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\io.py',
   'PYMODULE'),
  ('genericpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\genericpath.py',
   'PYMODULE'),
  ('sre_constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('posixpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\posixpath.py',
   'PYMODULE'),
  ('sre_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('sre_parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sre_parse.py',
   'PYMODULE'),
  ('locale',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\locale.py',
   'PYMODULE'),
  ('reprlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\reprlib.py',
   'PYMODULE'),
  ('types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\types.py',
   'PYMODULE'),
  ('copyreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\copyreg.py',
   'PYMODULE'),
  ('_collections_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('re._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('stat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\stat.py',
   'PYMODULE'),
  ('weakref',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\weakref.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\abc.py',
   'PYMODULE'),
  ('linecache',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\linecache.py',
   'PYMODULE'),
  ('codecs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\codecs.py',
   'PYMODULE'),
  ('enum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\enum.py',
   'PYMODULE'),
  ('heapq',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\heapq.py',
   'PYMODULE'),
  ('_weakrefset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('operator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\operator.py',
   'PYMODULE'),
  ('re',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\re\\__init__.py',
   'PYMODULE'),
  ('os',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\os.py',
   'PYMODULE')])
