#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目接单管理工具打包脚本
使用PyInstaller将项目打包为exe文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def clean_build():
    """清理之前的构建文件"""
    print("🧹 清理之前的构建文件...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"  删除目录: {dir_name}")
    
    # 清理.pyc文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))
                print(f"  删除文件: {os.path.join(root, file)}")


def check_dependencies():
    """检查依赖项"""
    print("📦 检查依赖项...")
    
    required_packages = [
        'PyQt5',
        'matplotlib',
        'pyinstaller'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️  缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def build_exe():
    """构建exe文件"""
    print("🔨 开始构建exe文件...")
    
    # 使用spec文件构建
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        '--noconfirm',
        'ProjectManager.spec'
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8')
        print("✅ 构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败!")
        print(f"错误信息: {e.stderr}")
        return False


def copy_resources():
    """复制资源文件到dist目录"""
    print("📁 复制资源文件...")
    
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    # 复制数据库文件（如果存在）
    db_file = 'demo_project_orders.db'
    if os.path.exists(db_file):
        shutil.copy2(db_file, dist_dir / db_file)
        print(f"  复制: {db_file}")
    
    # 创建README文件
    readme_content = """# 项目接单管理工具

## 使用说明

1. 双击"项目接单管理工具.exe"启动程序
2. 首次运行会自动创建数据库文件
3. 可以通过菜单栏进行数据的导入导出

## 功能特性

- 项目记录管理（增删改查）
- 数据筛选和搜索
- Markdown格式导入导出
- 统计图表分析
- 多种编程语言支持

## 技术支持

如有问题，请联系开发团队。

版本: 2.0.0
构建时间: """ + str(Path().resolve()) + """
"""
    
    with open(dist_dir / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("  创建: README.txt")
    
    return True


def main():
    """主函数"""
    print("🚀 项目接单管理工具打包程序")
    print("=" * 50)
    
    # 检查依赖项
    if not check_dependencies():
        return 1
    
    # 清理构建文件
    clean_build()
    
    # 构建exe
    if not build_exe():
        return 1
    
    # 复制资源文件
    if not copy_resources():
        return 1
    
    print("\n🎉 打包完成!")
    print(f"📂 输出目录: {os.path.abspath('dist')}")
    
    # 显示生成的文件
    dist_dir = Path('dist')
    if dist_dir.exists():
        print("\n📋 生成的文件:")
        for file in dist_dir.iterdir():
            if file.is_file():
                size = file.stat().st_size / (1024 * 1024)  # MB
                print(f"  {file.name} ({size:.1f} MB)")
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
