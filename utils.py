#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块
包含数据处理、导入导出等工具函数
"""

import re
from datetime import datetime
from typing import List, Dict, Tuple
from config import COLUMN_MAPPINGS, CHINESE_NUMBERS, PROGRAMMING_LANGUAGES, EXPORT_DATE_FORMAT


def truncate_text(text: str, max_length: int) -> str:
    """截断文本并添加省略号"""
    if not text:
        return ""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."


def parse_amount_string(amount_str: str) -> float:
    """解析金额字符串，支持多种格式"""
    if not amount_str or not amount_str.strip():
        return 0.0

    # 移除空格
    amount_str = amount_str.strip()

    # 移除常见的货币符号和分隔符
    # 支持：¥1500, $1500, 1,500.00, 1500.00, 1500, ￥1500等
    cleaned_str = re.sub(r'[¥￥$€£,，、\s]', '', amount_str)

    # 处理可能的中文数字（简单处理）
    # 如果包含中文数字，尝试简单转换
    for chinese, arabic in CHINESE_NUMBERS.items():
        if chinese in cleaned_str:
            cleaned_str = cleaned_str.replace(chinese, arabic)

    # 尝试提取数字
    # 匹配整数或小数
    number_pattern = r'(\d+(?:\.\d+)?)'
    matches = re.findall(number_pattern, cleaned_str)

    if matches:
        try:
            # 取第一个匹配的数字
            return float(matches[0])
        except ValueError:
            pass

    # 如果以上都失败，尝试直接转换
    try:
        return float(cleaned_str)
    except ValueError:
        print(f"无法解析金额: '{amount_str}' -> '{cleaned_str}'")
        return 0.0


def generate_markdown_content(language_groups: Dict) -> str:
    """生成Markdown内容"""
    content = []

    # 添加标题和说明
    content.append("# 项目接单记录")
    content.append("")
    content.append(f"**导出时间：** {datetime.now().strftime(EXPORT_DATE_FORMAT)}")
    content.append("")

    # 统计信息
    total_orders = sum(len(orders) for orders in language_groups.values())
    total_amount = sum(order['amount'] for orders in language_groups.values() for order in orders)
    content.append("## 📊 统计概览")
    content.append("")
    content.append(f"- **总项目数：** {total_orders}")
    content.append(f"- **总金额：** ¥{total_amount:.2f}")
    content.append(f"- **平均金额：** ¥{total_amount/total_orders:.2f}")
    content.append(f"- **编程语言种类：** {len(language_groups)}")
    content.append("")

    # 按编程语言分组显示
    for language, orders in sorted(language_groups.items()):
        content.append(f"## {language}")
        content.append("")

        # 语言统计
        lang_total = sum(order['amount'] for order in orders)
        content.append(f"**项目数量：** {len(orders)} | **总金额：** ¥{lang_total:.2f}")
        content.append("")

        # 表格头
        content.append("| 入库编号 | 交付日期 | 需求描述 | 金额 | 是否需要报告 | 联系方式 | 备注 |")
        content.append("|---------|---------|---------|------|-------------|---------|------|")

        # 表格数据
        for order in sorted(orders, key=lambda x: x['order_number']):
            desc = order['requirement_desc'].replace('|', '\\|').replace('\n', ' ')[:50]
            if len(order['requirement_desc']) > 50:
                desc += "..."

            remarks = (order['remarks'] or '').replace('|', '\\|').replace('\n', ' ')[:30]
            if order['remarks'] and len(order['remarks']) > 30:
                remarks += "..."

            report_text = "是" if order['need_report'] else "否"

            content.append(f"| {order['order_number']} | {order['delivery_date']} | {desc} | ¥{order['amount']:.2f} | {report_text} | {order['contact_info']} | {remarks} |")

        content.append("")

    # 添加页脚
    content.append("---")
    content.append("")
    content.append("*此文档由项目接单管理工具自动生成*")

    return '\n'.join(content)


def parse_markdown_content(content: str) -> List[Dict]:
    """解析Markdown内容，支持多种表格格式"""
    orders = []
    lines = content.split('\n')
    current_language = None

    print(f"开始解析Markdown内容，共 {len(lines)} 行")

    i = 0
    while i < len(lines):
        line = lines[i].strip()

        # 检测编程语言标题（支持 ## 格式）
        if line.startswith('##') and not line.startswith('###'):
            # 提取语言名称
            lang_match = re.search(r'##\s*(.+)', line)
            if lang_match:
                potential_lang = lang_match.group(1).strip()
                print(f"发现潜在语言标题: '{potential_lang}'")

                # 移除可能的emoji和特殊字符
                potential_lang = re.sub(r'[📊💻📈🔝🔻📋📄📅]', '', potential_lang).strip()
                print(f"清理后的语言名称: '{potential_lang}'")

                # 检查是否是已知的编程语言
                if potential_lang in PROGRAMMING_LANGUAGES:
                    current_language = potential_lang
                    print(f"设置当前语言: {current_language}")
                elif any(lang in potential_lang for lang in PROGRAMMING_LANGUAGES):
                    # 模糊匹配
                    for lang in PROGRAMMING_LANGUAGES:
                        if lang in potential_lang:
                            current_language = lang
                            print(f"模糊匹配到语言: {current_language}")
                            break

        # 检测表格头 - 更严格的检测条件
        elif ('|' in line and
              line.startswith('|') and
              line.endswith('|') and
              ('编号' in line or '日期' in line) and
              ('金额' in line or '联系方式' in line or '备注' in line)):
            print(f"发现表格头: '{line}'")
            print(f"当前语言: {current_language}")

            # 解析表格
            table_orders = parse_table_from_lines(lines[i:], current_language)
            print(f"从表格解析出 {len(table_orders)} 条记录")
            orders.extend(table_orders)

            # 跳过已处理的表格行
            j = i + 1
            while j < len(lines) and ('|' in lines[j] or lines[j].strip() == ''):
                j += 1
            i = j - 1

        i += 1

    print(f"总共解析出 {len(orders)} 条记录")
    return orders


def parse_table_from_lines(lines: List[str], language: str) -> List[Dict]:
    """从表格行中解析数据"""
    orders = []
    header_line = None
    separator_line = None

    print(f"解析表格，语言: {language}")

    # 找到表头和分隔符
    for i, line in enumerate(lines):
        line_stripped = line.strip()
        if ('|' in line_stripped and
            header_line is None and
            line_stripped.startswith('|') and
            line_stripped.endswith('|') and
            not line_stripped.startswith('|---')):  # 不是分隔符行
            header_line = i
            print(f"找到表头行 {i}: '{line_stripped}'")
        elif '|' in line_stripped and '-' in line_stripped and header_line is not None:
            separator_line = i
            print(f"找到分隔符行 {i}: '{line_stripped}'")
            break

    if header_line is None:
        print("未找到表头行")
        return orders

    # 解析表头
    header_cells = [cell.strip() for cell in lines[header_line].split('|') if cell.strip()]
    print(f"表头单元格: {header_cells}")

    # 映射列名到字段名
    field_mapping = {}
    for i, header in enumerate(header_cells):
        for col_name, field_name in COLUMN_MAPPINGS.items():
            if col_name in header:
                field_mapping[i] = field_name
                print(f"列 {i} '{header}' 映射到字段 '{field_name}'")
                break

    print(f"字段映射: {field_mapping}")

    # 解析数据行
    start_line = separator_line + 1 if separator_line else header_line + 1
    for line in lines[start_line:]:
        if not line.strip() or '|' not in line:
            break

        cells = [cell.strip() for cell in line.split('|') if cell.strip()]
        if len(cells) < 3:  # 至少需要3列数据
            continue

        order = {
            'programming_language': language or '未知',
            'order_number': '',
            'delivery_date': '',
            'requirement_desc': '',
            'amount': 0.0,
            'need_report': False,
            'contact_info': '',
            'remarks': ''
        }

        # 填充数据
        for i, cell in enumerate(cells):
            if i in field_mapping:
                field_name = field_mapping[i]

                # 清理单元格内容
                cell = cell.strip()

                if field_name == 'amount':
                    # 解析金额 - 增强版本
                    amount_value = parse_amount_string(cell)
                    order[field_name] = amount_value

                elif field_name == 'need_report':
                    # 解析布尔值
                    order[field_name] = cell in ['是', 'True', 'true', '1', '需要']

                elif field_name == 'delivery_date':
                    # 验证日期格式
                    try:
                        datetime.strptime(cell, '%Y-%m-%d')
                        order[field_name] = cell
                    except ValueError:
                        # 尝试其他日期格式
                        try:
                            date_obj = datetime.strptime(cell, '%Y/%m/%d')
                            order[field_name] = date_obj.strftime('%Y-%m-%d')
                        except ValueError:
                            order[field_name] = datetime.now().strftime('%Y-%m-%d')

                elif field_name in ['requirement_desc', 'remarks']:
                    # 处理可能被截断的描述和备注
                    # 移除末尾的"..."
                    if cell.endswith('...'):
                        cell = cell[:-3]
                    order[field_name] = cell

                else:
                    order[field_name] = cell

        # 验证必要字段
        if order['order_number'] and order['requirement_desc']:
            orders.append(order)

    return orders


def execute_import(db_manager, orders: List[Dict]) -> Tuple[int, int]:
    """执行导入操作"""
    success_count = 0
    error_count = 0

    for order in orders:
        try:
            if db_manager.add_order(order):
                success_count += 1
            else:
                error_count += 1
        except Exception:
            error_count += 1

    return success_count, error_count
